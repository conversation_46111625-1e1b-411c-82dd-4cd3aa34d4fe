<?php
/**
 * Configuration file for Dashboard Log BPJS
 * 
 * This file contains all configuration settings for the dashboard.
 * Copy this file to config.local.php and modify as needed for your environment.
 */

// Environment settings
define('ENVIRONMENT', 'development'); // development, staging, production

// Database configuration
define('DB_HOST', '***********');
define('DB_USERNAME', 'bpjs');
define('DB_PASSWORD', 'simpel');
define('DB_NAME', 'remun_medis');
define('DB_CHARSET', 'utf8mb4');

// API configuration
define('API_BASE_URL', '*************:8005/api/');
define('API_TIMEOUT', 30);

// Application settings
define('APP_NAME', 'Dashboard Log BPJS');
define('APP_VERSION', '1.0.0');
define('APP_TIMEZONE', 'Asia/Jakarta');

// Pagination settings
define('DEFAULT_PAGE_SIZE', 50);
define('MAX_PAGE_SIZE', 100);

// Log file settings
define('LOG_FILE_PATH', 'log-surat-kontrol.txt');
define('LOG_MAX_SIZE', 10485760); // 10MB

// Security settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('CSRF_TOKEN_EXPIRE', 1800); // 30 minutes

// Cache settings
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 300); // 5 minutes

// Debug settings
if (ENVIRONMENT === 'development') {
    define('DEBUG_MODE', true);
    define('SHOW_ERRORS', true);
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    define('DEBUG_MODE', false);
    define('SHOW_ERRORS', false);
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    error_reporting(0);
}

// Set timezone
date_default_timezone_set(APP_TIMEZONE);

/**
 * Get database connection
 */
function getDbConnection() {
    static $connection = null;
    
    if ($connection === null) {
        try {
            $connection = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME);
            
            if ($connection->connect_error) {
                throw new Exception("Connection failed: " . $connection->connect_error);
            }
            
            $connection->set_charset(DB_CHARSET);
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                die("Database connection error: " . $e->getMessage());
            } else {
                die("Database connection error. Please contact administrator.");
            }
        }
    }
    
    return $connection;
}

/**
 * Log error messages
 */
function logError($message, $file = '', $line = '') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] ERROR: $message";
    
    if ($file) {
        $logMessage .= " in $file";
    }
    
    if ($line) {
        $logMessage .= " on line $line";
    }
    
    $logMessage .= PHP_EOL;
    
    error_log($logMessage, 3, 'error.log');
}

/**
 * Send JSON response
 */
function sendJsonResponse($data, $httpCode = 200) {
    http_response_code($httpCode);
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
    
    echo json_encode($data);
    exit;
}

/**
 * Validate and sanitize input
 */
function sanitizeInput($input, $type = 'string') {
    switch ($type) {
        case 'int':
            return filter_var($input, FILTER_VALIDATE_INT);
        case 'email':
            return filter_var($input, FILTER_VALIDATE_EMAIL);
        case 'url':
            return filter_var($input, FILTER_VALIDATE_URL);
        case 'date':
            $date = DateTime::createFromFormat('Y-m-d', $input);
            return $date && $date->format('Y-m-d') === $input ? $input : false;
        case 'datetime':
            $date = DateTime::createFromFormat('Y-m-d H:i:s', $input);
            return $date && $date->format('Y-m-d H:i:s') === $input ? $input : false;
        default:
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

/**
 * Check if request is AJAX
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Generate CSRF token
 */
function generateCsrfToken() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    $_SESSION['csrf_token_time'] = time();
    
    return $token;
}

/**
 * Verify CSRF token
 */
function verifyCsrfToken($token) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }
    
    // Check if token has expired
    if (time() - $_SESSION['csrf_token_time'] > CSRF_TOKEN_EXPIRE) {
        unset($_SESSION['csrf_token']);
        unset($_SESSION['csrf_token_time']);
        return false;
    }
    
    return hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Format file size
 */
function formatFileSize($size) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, 2) . ' ' . $units[$i];
}

/**
 * Get client IP address
 */
function getClientIp() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, 
                    FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * Rate limiting
 */
function checkRateLimit($identifier, $maxRequests = 100, $timeWindow = 3600) {
    $cacheFile = "cache/rate_limit_" . md5($identifier) . ".txt";
    
    if (!file_exists('cache')) {
        mkdir('cache', 0755, true);
    }
    
    $currentTime = time();
    $requests = [];
    
    if (file_exists($cacheFile)) {
        $data = file_get_contents($cacheFile);
        $requests = json_decode($data, true) ?: [];
    }
    
    // Remove old requests outside time window
    $requests = array_filter($requests, function($timestamp) use ($currentTime, $timeWindow) {
        return ($currentTime - $timestamp) < $timeWindow;
    });
    
    // Check if limit exceeded
    if (count($requests) >= $maxRequests) {
        return false;
    }
    
    // Add current request
    $requests[] = $currentTime;
    
    // Save to cache
    file_put_contents($cacheFile, json_encode($requests));
    
    return true;
}

// Load local configuration if exists
if (file_exists('config.local.php')) {
    include 'config.local.php';
}
?>

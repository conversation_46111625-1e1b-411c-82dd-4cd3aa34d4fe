# Dashboard Log BPJS

Dashboard untuk menampilkan dan mengelola log dari berbagai operasi BPJS seperti surat kontrol, antrean, dan lainnya.

## Fitur Utama

### 📊 Dashboard Overview
- **Statistik Real-time**: Menampilkan total log, sukses, error, dan log hari ini
- **Navigasi Sidebar**: Menu navigasi untuk berbagai jenis log
- **Responsive Design**: Tampilan yang optimal di desktop dan mobile

### 🔍 Filter dan Pencarian
- **Filter Tanggal**: Filter berdasarkan rentang tanggal
- **Filter Status**: Filter berdasarkan kode status response
- **Pencarian**: Cari berdasarkan nomor kartu, nama pasien, atau data lainnya
- **Reset Filter**: Tombol untuk mereset semua filter

### 📋 Jenis Log yang Didukung
1. **Log Surat Kontrol**: Log dari file `log-surat-kontrol.txt`
2. **Log Batal Surat Kontrol**: Log pembatalan surat kontrol
3. **Log Tambah Antrean**: Log penambahan antrean pasien
4. **Log Batal Antrean**: Log pembatalan antrean
5. **Log Update Antrean**: Log update status antrean

### 📱 Detail Log
- **Modal Detail**: Popup untuk melihat detail lengkap setiap log
- **Format JSON**: Request dan response dalam format JSON yang mudah dibaca
- **Informasi Lengkap**: Menampilkan semua data terkait log

## Struktur File

```
/var/www/html/bpjs/
├── dashboard.php          # File utama dashboard
├── api/
│   └── logs.php          # API endpoint untuk data log
├── js/
│   └── dashboard.js      # JavaScript untuk interaksi dashboard
├── css/
│   └── dashboard.css     # Styling khusus dashboard
├── Helper.php            # Class helper untuk koneksi database
└── README.md            # Dokumentasi ini
```

## Instalasi dan Setup

### 1. Persyaratan Sistem
- PHP 7.4 atau lebih tinggi
- MySQL/MariaDB
- Web server (Apache/Nginx)
- Browser modern dengan JavaScript enabled

### 2. Konfigurasi Database
Pastikan tabel-tabel log berikut sudah ada di database:

```sql
-- Tabel log batal surat kontrol
CREATE TABLE IF NOT EXISTS remun_medis.log_batal_surat_kontrol (
    uuid VARCHAR(36) PRIMARY KEY,
    suratkontrol VARCHAR(50),
    request TEXT,
    response TEXT,
    code VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel log tambah antrean
CREATE TABLE IF NOT EXISTS remun_medis.log_add_antrean (
    uuid VARCHAR(36) PRIMARY KEY,
    id_perjanjian INT,
    request TEXT,
    response TEXT,
    code VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel log batal antrean
CREATE TABLE IF NOT EXISTS remun_medis.log_batal_antrean (
    uuid VARCHAR(36) PRIMARY KEY,
    id_perjanjian INT,
    request TEXT,
    response TEXT,
    code VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel log update antrean
CREATE TABLE IF NOT EXISTS remun_medis.log_update_antrean (
    uuid VARCHAR(36) PRIMARY KEY,
    id_perjanjian INT,
    task_id INT,
    tanggal DATE,
    request TEXT,
    response TEXT,
    code VARCHAR(10),
    waktu TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Konfigurasi Koneksi Database
Edit file `Helper.php` untuk menyesuaikan konfigurasi database:

```php
public function conn(){
    $servername = "192.168.7.3";  // Sesuaikan dengan server database
    $username = "bpjs";           // Username database
    $password = "simpel";         // Password database
    $dbname = "remun_medis";      // Nama database
    
    // ... kode lainnya
}
```

### 4. Akses Dashboard
Buka browser dan akses: `http://your-domain/bpjs/dashboard.php`

## Penggunaan

### 1. Navigasi Dashboard
- **Sidebar**: Klik menu di sidebar untuk memilih jenis log
- **Mobile**: Gunakan tombol hamburger untuk membuka sidebar di mobile

### 2. Filter Data
- **Tanggal**: Pilih rentang tanggal untuk filter
- **Status**: Pilih status response untuk filter
- **Pencarian**: Ketik kata kunci di kolom pencarian
- **Terapkan**: Klik tombol "Terapkan Filter"
- **Reset**: Klik tombol "Reset" untuk menghapus semua filter

### 3. Melihat Detail Log
- Klik tombol "Detail" pada baris log yang ingin dilihat
- Modal akan terbuka menampilkan informasi lengkap
- Scroll untuk melihat request dan response JSON

### 4. Refresh Data
- Klik tombol "Refresh" untuk memperbarui data terbaru

## API Endpoints

### GET `/api/logs.php`
Mengambil data log dengan parameter:
- `type`: Jenis log (all, surat_kontrol, batal_surat_kontrol, dll)
- `page`: Halaman data (default: 1)
- `limit`: Jumlah data per halaman (default: 50)
- `date_from`: Filter tanggal mulai (format: Y-m-d)
- `date_to`: Filter tanggal akhir (format: Y-m-d)
- `status`: Filter status code
- `search`: Kata kunci pencarian

### GET `/api/logs.php?action=stats`
Mengambil statistik log (total, sukses, error, hari ini)

### GET `/api/logs.php?action=detail`
Mengambil detail log dengan parameter:
- `id`: ID log
- `log_type`: Jenis log

## Troubleshooting

### 1. Data Tidak Muncul
- Periksa koneksi database di `Helper.php`
- Pastikan tabel log sudah ada dan memiliki data
- Cek console browser untuk error JavaScript

### 2. Error 500
- Periksa log error PHP
- Pastikan semua file PHP memiliki permission yang benar
- Cek konfigurasi web server

### 3. Tampilan Tidak Responsif
- Pastikan file CSS dimuat dengan benar
- Cek console browser untuk error loading CSS
- Refresh cache browser

### 4. Filter Tidak Berfungsi
- Pastikan JavaScript dimuat dengan benar
- Cek format tanggal yang digunakan
- Periksa parameter yang dikirim ke API

## Pengembangan Lebih Lanjut

### 1. Menambah Jenis Log Baru
1. Tambahkan method baru di class `LogAPI` di `api/logs.php`
2. Update menu sidebar di `dashboard.php`
3. Tambahkan case baru di JavaScript `dashboard.js`

### 2. Menambah Filter Baru
1. Tambahkan input filter di section filter
2. Update parameter di JavaScript
3. Modifikasi query di API sesuai filter baru

### 3. Export Data
Bisa ditambahkan fitur export ke Excel/PDF dengan library seperti:
- PhpSpreadsheet untuk Excel
- TCPDF untuk PDF

### 4. Real-time Updates
Implementasi WebSocket atau polling untuk update real-time

## Kontribusi

Untuk berkontribusi pada pengembangan dashboard ini:
1. Fork repository
2. Buat branch fitur baru
3. Commit perubahan
4. Push ke branch
5. Buat Pull Request

## Lisensi

Dashboard ini dikembangkan untuk keperluan internal BPJS dan tidak untuk distribusi komersial.

## Support

Untuk bantuan teknis atau pertanyaan, hubungi tim IT BPJS.

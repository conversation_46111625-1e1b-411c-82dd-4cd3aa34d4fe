<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

ini_set('display_errors', '1');
ini_set('display_startup_errors', '1');
error_reporting(E_ALL);

require '../Helper.php';

class LogAPI {
    private $conn;
    private $helper;

    public function __construct() {
        $this->helper = new Helper();
        $this->conn = $this->helper->conn();
    }

    public function getLogs() {
        $logType = $_GET['type'] ?? 'all';
        $dateFrom = $_GET['date_from'] ?? '';
        $dateTo = $_GET['date_to'] ?? '';
        $status = $_GET['status'] ?? '';
        $search = $_GET['search'] ?? '';
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 50);
        $offset = ($page - 1) * $limit;

        try {
            $logs = [];
            $totalCount = 0;

            switch ($logType) {
                case 'surat_kontrol':
                    $result = $this->getSuratKontrolLogs($dateFrom, $dateTo, $status, $search, $limit, $offset);
                    break;
                case 'batal_surat_kontrol':
                    $result = $this->getBatalSuratKontrolLogs($dateFrom, $dateTo, $status, $search, $limit, $offset);
                    break;
                case 'add_antrean':
                    $result = $this->getAddAntreanLogs($dateFrom, $dateTo, $status, $search, $limit, $offset);
                    break;
                case 'batal_antrean':
                    $result = $this->getBatalAntreanLogs($dateFrom, $dateTo, $status, $search, $limit, $offset);
                    break;
                case 'update_antrean':
                    $result = $this->getUpdateAntreanLogs($dateFrom, $dateTo, $status, $search, $limit, $offset);
                    break;
                default:
                    $result = $this->getAllLogs($dateFrom, $dateTo, $status, $search, $limit, $offset);
                    break;
            }

            echo json_encode([
                'success' => true,
                'data' => $result['logs'],
                'total' => $result['total'],
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($result['total'] / $limit)
            ]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    private function getSuratKontrolLogs($dateFrom, $dateTo, $status, $search, $limit, $offset) {
        // Untuk log surat kontrol, kita ambil dari file log-surat-kontrol.txt
        $logs = [];
        $total = 0;
        
        if (file_exists('../log-surat-kontrol.txt')) {
            $content = file_get_contents('../log-surat-kontrol.txt');
            $lines = explode("\n", $content);
            
            foreach ($lines as $line) {
                if (empty(trim($line))) continue;
                
                // Parse log line: timestamp + JSON
                if (preg_match('/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (.+)$/', $line, $matches)) {
                    $timestamp = $matches[1];
                    $jsonData = json_decode($matches[2], true);
                    
                    if ($jsonData) {
                        $logDate = date('Y-m-d', strtotime($timestamp));
                        
                        // Apply filters
                        if ($dateFrom && $logDate < $dateFrom) continue;
                        if ($dateTo && $logDate > $dateTo) continue;
                        if ($status && isset($jsonData['response']['metaData']['code']) && $jsonData['response']['metaData']['code'] != $status) continue;
                        if ($search && stripos(json_encode($jsonData), $search) === false) continue;
                        
                        $logs[] = [
                            'id' => md5($line),
                            'tanggal' => $timestamp,
                            'jenis_log' => 'Surat Kontrol',
                            'status' => $jsonData['response']['metaData']['code'] ?? 'Unknown',
                            'nomor_kartu' => $jsonData['nomorkartu'] ?? '',
                            'nama_pasien' => '',
                            'detail' => $jsonData
                        ];
                    }
                }
            }
            
            $total = count($logs);
            $logs = array_slice($logs, $offset, $limit);
        }
        
        return ['logs' => $logs, 'total' => $total];
    }

    private function getBatalSuratKontrolLogs($dateFrom, $dateTo, $status, $search, $limit, $offset) {
        // First check if table exists and get its structure
        $checkTableQuery = "SHOW TABLES LIKE 'log_batal_surat_kontrol'";
        $checkResult = $this->conn->query($checkTableQuery);

        if ($checkResult->num_rows == 0) {
            // Table doesn't exist, return empty result
            return ['logs' => [], 'total' => 0];
        }

        // Get table columns
        $columnsQuery = "SHOW COLUMNS FROM remun_medis.log_batal_surat_kontrol";
        $columnsResult = $this->conn->query($columnsQuery);
        $columns = [];
        while ($col = $columnsResult->fetch_assoc()) {
            $columns[] = $col['Field'];
        }

        // Determine date column (created_at or other timestamp column)
        $dateColumn = 'uuid'; // fallback
        if (in_array('created_at', $columns)) {
            $dateColumn = 'created_at';
        } elseif (in_array('timestamp', $columns)) {
            $dateColumn = 'timestamp';
        } elseif (in_array('waktu', $columns)) {
            $dateColumn = 'waktu';
        }

        $whereConditions = ['1=1'];
        $params = [];

        if ($dateFrom && $dateColumn != 'uuid') {
            $whereConditions[] = "DATE($dateColumn) >= ?";
            $params[] = $dateFrom;
        }
        if ($dateTo && $dateColumn != 'uuid') {
            $whereConditions[] = "DATE($dateColumn) <= ?";
            $params[] = $dateTo;
        }
        if ($status && in_array('code', $columns)) {
            $whereConditions[] = "code = ?";
            $params[] = $status;
        }
        if ($search) {
            $searchConditions = [];
            if (in_array('suratkontrol', $columns)) {
                $searchConditions[] = "suratkontrol LIKE ?";
                $params[] = "%$search%";
            }
            if (in_array('request', $columns)) {
                $searchConditions[] = "request LIKE ?";
                $params[] = "%$search%";
            }
            if (in_array('response', $columns)) {
                $searchConditions[] = "response LIKE ?";
                $params[] = "%$search%";
            }
            if (!empty($searchConditions)) {
                $whereConditions[] = "(" . implode(' OR ', $searchConditions) . ")";
            }
        }

        $whereClause = implode(' AND ', $whereConditions);

        // Count total
        $countQuery = "SELECT COUNT(*) as total FROM remun_medis.log_batal_surat_kontrol WHERE $whereClause";
        $countStmt = $this->conn->prepare($countQuery);

        if (!$countStmt) {
            error_log("Prepare failed for count query: " . $this->conn->error);
            return ['logs' => [], 'total' => 0];
        }

        if (!empty($params)) {
            $types = str_repeat('s', count($params));
            if (!$countStmt->bind_param($types, ...$params)) {
                error_log("Bind param failed for count query: " . $countStmt->error);
                return ['logs' => [], 'total' => 0];
            }
        }

        if (!$countStmt->execute()) {
            error_log("Execute failed for count query: " . $countStmt->error);
            return ['logs' => [], 'total' => 0];
        }

        $countResult = $countStmt->get_result();
        $total = $countResult ? $countResult->fetch_assoc()['total'] : 0;

        // Build select columns
        $selectColumns = ['uuid'];
        if (in_array('suratkontrol', $columns)) $selectColumns[] = 'suratkontrol';
        if (in_array('request', $columns)) $selectColumns[] = 'request';
        if (in_array('response', $columns)) $selectColumns[] = 'response';
        if (in_array('code', $columns)) $selectColumns[] = 'code';
        if ($dateColumn != 'uuid') $selectColumns[] = $dateColumn;

        // Get data
        $query = "SELECT " . implode(', ', $selectColumns) . "
                  FROM remun_medis.log_batal_surat_kontrol
                  WHERE $whereClause
                  ORDER BY " . ($dateColumn != 'uuid' ? $dateColumn : 'uuid') . " DESC
                  LIMIT ? OFFSET ?";

        $dataParams = $params;
        $dataParams[] = $limit;
        $dataParams[] = $offset;

        $stmt = $this->conn->prepare($query);
        if (!$stmt) {
            error_log("Prepare failed for data query: " . $this->conn->error);
            return ['logs' => [], 'total' => $total];
        }

        $types = str_repeat('s', count($params)) . 'ii';
        if (!$stmt->bind_param($types, ...$dataParams)) {
            error_log("Bind param failed for data query: " . $stmt->error);
            return ['logs' => [], 'total' => $total];
        }

        if (!$stmt->execute()) {
            error_log("Execute failed for data query: " . $stmt->error);
            return ['logs' => [], 'total' => $total];
        }

        $result = $stmt->get_result();
        if (!$result) {
            error_log("Get result failed: " . $stmt->error);
            return ['logs' => [], 'total' => $total];
        }

        $logs = [];
        while ($row = $result->fetch_assoc()) {
            $requestData = isset($row['request']) ? json_decode($row['request'], true) : null;
            $responseData = isset($row['response']) ? json_decode($row['response'], true) : null;

            $logs[] = [
                'id' => $row['uuid'],
                'tanggal' => $row[$dateColumn] ?? date('Y-m-d H:i:s'),
                'jenis_log' => 'Batal Surat Kontrol',
                'status' => $row['code'] ?? 'Unknown',
                'nomor_kartu' => '',
                'nama_pasien' => '',
                'detail' => [
                    'suratkontrol' => $row['suratkontrol'] ?? '',
                    'request' => $requestData,
                    'response' => $responseData,
                    'code' => $row['code'] ?? 'Unknown'
                ]
            ];
        }

        return ['logs' => $logs, 'total' => $total];
    }

    private function getAddAntreanLogs($dateFrom, $dateTo, $status, $search, $limit, $offset) {
        // Check if table exists
        $checkTableQuery = "SHOW TABLES LIKE 'log_add_antrean'";
        $checkResult = $this->conn->query($checkTableQuery);

        if ($checkResult->num_rows == 0) {
            return ['logs' => [], 'total' => 0];
        }

        $whereConditions = ['1=1'];
        $params = [];

        // Use a more flexible approach for date filtering
        if ($dateFrom) {
            $whereConditions[] = "(DATE(created_at) >= ? OR DATE(waktu) >= ?)";
            $params[] = $dateFrom;
            $params[] = $dateFrom;
        }
        if ($dateTo) {
            $whereConditions[] = "(DATE(created_at) <= ? OR DATE(waktu) <= ?)";
            $params[] = $dateTo;
            $params[] = $dateTo;
        }
        if ($status) {
            $whereConditions[] = "code = ?";
            $params[] = $status;
        }
        if ($search) {
            $whereConditions[] = "(id_perjanjian LIKE ? OR request LIKE ? OR response LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }

        $whereClause = implode(' AND ', $whereConditions);

        // Count total with error handling
        $countQuery = "SELECT COUNT(*) as total FROM remun_medis.log_add_antrean WHERE $whereClause";
        $countStmt = $this->conn->prepare($countQuery);

        if (!$countStmt) {
            error_log("Prepare failed for log_add_antrean count: " . $this->conn->error);
            return ['logs' => [], 'total' => 0];
        }

        if (!empty($params)) {
            $types = str_repeat('s', count($params));
            if (!$countStmt->bind_param($types, ...$params)) {
                error_log("Bind param failed for log_add_antrean count: " . $countStmt->error);
                return ['logs' => [], 'total' => 0];
            }
        }

        if (!$countStmt->execute()) {
            error_log("Execute failed for log_add_antrean count: " . $countStmt->error);
            return ['logs' => [], 'total' => 0];
        }

        $countResult = $countStmt->get_result();
        $total = $countResult ? $countResult->fetch_assoc()['total'] : 0;

        // Get data with error handling
        $query = "SELECT uuid, id_perjanjian, request, response, code,
                         COALESCE(created_at, waktu) as tanggal
                  FROM remun_medis.log_add_antrean
                  WHERE $whereClause
                  ORDER BY COALESCE(created_at, waktu) DESC
                  LIMIT ? OFFSET ?";

        $dataParams = $params;
        $dataParams[] = $limit;
        $dataParams[] = $offset;

        $stmt = $this->conn->prepare($query);
        if (!$stmt) {
            error_log("Prepare failed for log_add_antrean data: " . $this->conn->error);
            return ['logs' => [], 'total' => $total];
        }

        $types = str_repeat('s', count($params)) . 'ii';
        if (!$stmt->bind_param($types, ...$dataParams)) {
            error_log("Bind param failed for log_add_antrean data: " . $stmt->error);
            return ['logs' => [], 'total' => $total];
        }

        if (!$stmt->execute()) {
            error_log("Execute failed for log_add_antrean data: " . $stmt->error);
            return ['logs' => [], 'total' => $total];
        }

        $result = $stmt->get_result();
        if (!$result) {
            return ['logs' => [], 'total' => $total];
        }

        $logs = [];
        while ($row = $result->fetch_assoc()) {
            $requestData = json_decode($row['request'], true);
            $responseData = json_decode($row['response'], true);

            $logs[] = [
                'id' => $row['uuid'],
                'tanggal' => $row['tanggal'] ?? date('Y-m-d H:i:s'),
                'jenis_log' => 'Tambah Antrean',
                'status' => $row['code'] ?? 'Unknown',
                'nomor_kartu' => '',
                'nama_pasien' => '',
                'detail' => [
                    'id_perjanjian' => $row['id_perjanjian'],
                    'request' => $requestData,
                    'response' => $responseData,
                    'code' => $row['code']
                ]
            ];
        }

        return ['logs' => $logs, 'total' => $total];
    }

    private function getBatalAntreanLogs($dateFrom, $dateTo, $status, $search, $limit, $offset) {
        $whereConditions = ['1=1'];
        $params = [];

        if ($dateFrom) {
            $whereConditions[] = "DATE(created_at) >= ?";
            $params[] = $dateFrom;
        }
        if ($dateTo) {
            $whereConditions[] = "DATE(created_at) <= ?";
            $params[] = $dateTo;
        }
        if ($status) {
            $whereConditions[] = "code = ?";
            $params[] = $status;
        }
        if ($search) {
            $whereConditions[] = "(id_perjanjian LIKE ? OR request LIKE ? OR response LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }

        $whereClause = implode(' AND ', $whereConditions);

        // Count total
        $countQuery = "SELECT COUNT(*) as total FROM remun_medis.log_batal_antrean WHERE $whereClause";
        $countStmt = $this->conn->prepare($countQuery);
        if ($params) {
            $countStmt->bind_param(str_repeat('s', count($params)), ...$params);
        }
        $countStmt->execute();
        $total = $countStmt->get_result()->fetch_assoc()['total'];

        // Get data with patient info
        $query = "SELECT la.uuid, la.id_perjanjian, la.request, la.response, la.code, la.created_at,
                         p.NAMAPASIEN, kap.NOMOR as nomor_kartu
                  FROM remun_medis.log_batal_antrean la
                  LEFT JOIN remun_medis.perjanjian p ON la.id_perjanjian = p.ID
                  LEFT JOIN master.kartu_asuransi_pasien kap ON p.NOMR = kap.NORM AND kap.JENIS = 2
                  WHERE $whereClause 
                  ORDER BY la.created_at DESC 
                  LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param(str_repeat('s', count($params) - 2) . 'ii', ...$params);
        $stmt->execute();
        $result = $stmt->get_result();

        $logs = [];
        while ($row = $result->fetch_assoc()) {
            $requestData = json_decode($row['request'], true);
            $responseData = json_decode($row['response'], true);
            
            $logs[] = [
                'id' => $row['uuid'],
                'tanggal' => $row['created_at'],
                'jenis_log' => 'Batal Antrean',
                'status' => $row['code'],
                'nomor_kartu' => $row['nomor_kartu'] ?? '',
                'nama_pasien' => $row['NAMAPASIEN'] ?? '',
                'detail' => [
                    'id_perjanjian' => $row['id_perjanjian'],
                    'request' => $requestData,
                    'response' => $responseData,
                    'code' => $row['code']
                ]
            ];
        }

        return ['logs' => $logs, 'total' => $total];
    }

    private function getUpdateAntreanLogs($dateFrom, $dateTo, $status, $search, $limit, $offset) {
        $whereConditions = ['1=1'];
        $params = [];

        if ($dateFrom) {
            $whereConditions[] = "DATE(waktu) >= ?";
            $params[] = $dateFrom;
        }
        if ($dateTo) {
            $whereConditions[] = "DATE(waktu) <= ?";
            $params[] = $dateTo;
        }
        if ($status) {
            $whereConditions[] = "code = ?";
            $params[] = $status;
        }
        if ($search) {
            $whereConditions[] = "(id_perjanjian LIKE ? OR request LIKE ? OR response LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }

        $whereClause = implode(' AND ', $whereConditions);

        // Count total
        $countQuery = "SELECT COUNT(*) as total FROM remun_medis.log_update_antrean WHERE $whereClause";
        $countStmt = $this->conn->prepare($countQuery);
        if ($params) {
            $countStmt->bind_param(str_repeat('s', count($params)), ...$params);
        }
        $countStmt->execute();
        $total = $countStmt->get_result()->fetch_assoc()['total'];

        // Get data
        $query = "SELECT la.uuid, la.id_perjanjian, la.task_id, la.tanggal, la.request, la.response, la.code, la.waktu,
                         p.NAMAPASIEN, kap.NOMOR as nomor_kartu
                  FROM remun_medis.log_update_antrean la
                  LEFT JOIN remun_medis.perjanjian p ON la.id_perjanjian = p.ID
                  LEFT JOIN master.kartu_asuransi_pasien kap ON p.NOMR = kap.NORM AND kap.JENIS = 2
                  WHERE $whereClause 
                  ORDER BY la.waktu DESC 
                  LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param(str_repeat('s', count($params) - 2) . 'ii', ...$params);
        $stmt->execute();
        $result = $stmt->get_result();

        $logs = [];
        while ($row = $result->fetch_assoc()) {
            $requestData = json_decode($row['request'], true);
            $responseData = json_decode($row['response'], true);
            
            $logs[] = [
                'id' => $row['uuid'],
                'tanggal' => $row['waktu'],
                'jenis_log' => 'Update Antrean',
                'status' => $row['code'],
                'nomor_kartu' => $row['nomor_kartu'] ?? '',
                'nama_pasien' => $row['NAMAPASIEN'] ?? '',
                'detail' => [
                    'id_perjanjian' => $row['id_perjanjian'],
                    'task_id' => $row['task_id'],
                    'tanggal' => $row['tanggal'],
                    'request' => $requestData,
                    'response' => $responseData,
                    'code' => $row['code']
                ]
            ];
        }

        return ['logs' => $logs, 'total' => $total];
    }

    private function getAllLogs($dateFrom, $dateTo, $status, $search, $limit, $offset) {
        // Combine all log types
        $allLogs = [];
        
        // Get from each log type
        $suratKontrol = $this->getSuratKontrolLogs($dateFrom, $dateTo, $status, $search, 1000, 0);
        $batalSuratKontrol = $this->getBatalSuratKontrolLogs($dateFrom, $dateTo, $status, $search, 1000, 0);
        $addAntrean = $this->getAddAntreanLogs($dateFrom, $dateTo, $status, $search, 1000, 0);
        $batalAntrean = $this->getBatalAntreanLogs($dateFrom, $dateTo, $status, $search, 1000, 0);
        $updateAntrean = $this->getUpdateAntreanLogs($dateFrom, $dateTo, $status, $search, 1000, 0);
        
        $allLogs = array_merge(
            $suratKontrol['logs'],
            $batalSuratKontrol['logs'],
            $addAntrean['logs'],
            $batalAntrean['logs'],
            $updateAntrean['logs']
        );
        
        // Sort by date descending
        usort($allLogs, function($a, $b) {
            return strtotime($b['tanggal']) - strtotime($a['tanggal']);
        });
        
        $total = count($allLogs);
        $logs = array_slice($allLogs, $offset, $limit);
        
        return ['logs' => $logs, 'total' => $total];
    }

    public function getStats() {
        try {
            $today = date('Y-m-d');
            
            // Get stats from each table
            $stats = [
                'total' => 0,
                'success' => 0,
                'error' => 0,
                'today' => 0
            ];
            
            // Stats from log_batal_surat_kontrol
            $query = "SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN code IN ('200', '201') THEN 1 ELSE 0 END) as success,
                        SUM(CASE WHEN code NOT IN ('200', '201') THEN 1 ELSE 0 END) as error,
                        SUM(CASE WHEN DATE(created_at) = ? THEN 1 ELSE 0 END) as today
                      FROM remun_medis.log_batal_surat_kontrol";
            $stmt = $this->conn->prepare($query);
            $stmt->bind_param('s', $today);
            $stmt->execute();
            $result = $stmt->get_result()->fetch_assoc();
            
            $stats['total'] += $result['total'];
            $stats['success'] += $result['success'];
            $stats['error'] += $result['error'];
            $stats['today'] += $result['today'];
            
            // Add other tables...
            // Similar queries for other log tables
            
            echo json_encode([
                'success' => true,
                'data' => $stats
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function getLogDetail() {
        $logId = $_GET['id'] ?? '';
        $logType = $_GET['log_type'] ?? '';

        if (empty($logId)) {
            echo json_encode(['success' => false, 'message' => 'ID log tidak ditemukan']);
            return;
        }

        try {
            $detail = null;

            switch ($logType) {
                case 'Batal Surat Kontrol':
                    $detail = $this->getBatalSuratKontrolDetail($logId);
                    break;
                case 'Tambah Antrean':
                    $detail = $this->getAddAntreanDetail($logId);
                    break;
                case 'Batal Antrean':
                    $detail = $this->getBatalAntreanDetail($logId);
                    break;
                case 'Update Antrean':
                    $detail = $this->getUpdateAntreanDetail($logId);
                    break;
                case 'Surat Kontrol':
                    $detail = $this->getSuratKontrolDetail($logId);
                    break;
                default:
                    echo json_encode(['success' => false, 'message' => 'Jenis log tidak dikenali']);
                    return;
            }

            if ($detail) {
                echo json_encode(['success' => true, 'data' => $detail]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Detail log tidak ditemukan']);
            }

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    private function getBatalSuratKontrolDetail($uuid) {
        $query = "SELECT * FROM remun_medis.log_batal_surat_kontrol WHERE uuid = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param('s', $uuid);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();

        if ($result) {
            $result['request_formatted'] = json_decode($result['request'], true);
            $result['response_formatted'] = json_decode($result['response'], true);
        }

        return $result;
    }

    private function getAddAntreanDetail($uuid) {
        $query = "SELECT la.*, p.NAMAPASIEN, kap.NOMOR as nomor_kartu
                  FROM remun_medis.log_add_antrean la
                  LEFT JOIN remun_medis.perjanjian p ON la.id_perjanjian = p.ID
                  LEFT JOIN master.kartu_asuransi_pasien kap ON p.NOMR = kap.NORM AND kap.JENIS = 2
                  WHERE la.uuid = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param('s', $uuid);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();

        if ($result) {
            $result['request_formatted'] = json_decode($result['request'], true);
            $result['response_formatted'] = json_decode($result['response'], true);
        }

        return $result;
    }

    private function getBatalAntreanDetail($uuid) {
        $query = "SELECT la.*, p.NAMAPASIEN, kap.NOMOR as nomor_kartu
                  FROM remun_medis.log_batal_antrean la
                  LEFT JOIN remun_medis.perjanjian p ON la.id_perjanjian = p.ID
                  LEFT JOIN master.kartu_asuransi_pasien kap ON p.NOMR = kap.NORM AND kap.JENIS = 2
                  WHERE la.uuid = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param('s', $uuid);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();

        if ($result) {
            $result['request_formatted'] = json_decode($result['request'], true);
            $result['response_formatted'] = json_decode($result['response'], true);
        }

        return $result;
    }

    private function getUpdateAntreanDetail($uuid) {
        $query = "SELECT la.*, p.NAMAPASIEN, kap.NOMOR as nomor_kartu
                  FROM remun_medis.log_update_antrean la
                  LEFT JOIN remun_medis.perjanjian p ON la.id_perjanjian = p.ID
                  LEFT JOIN master.kartu_asuransi_pasien kap ON p.NOMR = kap.NORM AND kap.JENIS = 2
                  WHERE la.uuid = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param('s', $uuid);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();

        if ($result) {
            $result['request_formatted'] = json_decode($result['request'], true);
            $result['response_formatted'] = json_decode($result['response'], true);
        }

        return $result;
    }

    private function getSuratKontrolDetail($logId) {
        // For file-based logs, we need to parse the file again
        if (file_exists('../log-surat-kontrol.txt')) {
            $content = file_get_contents('../log-surat-kontrol.txt');
            $lines = explode("\n", $content);

            foreach ($lines as $line) {
                if (empty(trim($line))) continue;

                if (md5($line) === $logId) {
                    if (preg_match('/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (.+)$/', $line, $matches)) {
                        $timestamp = $matches[1];
                        $jsonData = json_decode($matches[2], true);

                        return [
                            'timestamp' => $timestamp,
                            'data' => $jsonData,
                            'raw_line' => $line
                        ];
                    }
                }
            }
        }

        return null;
    }
}

// Handle requests
$api = new LogAPI();

$action = $_GET['action'] ?? 'logs';

switch ($action) {
    case 'logs':
        $api->getLogs();
        break;
    case 'stats':
        $api->getStats();
        break;
    case 'detail':
        $api->getLogDetail();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}
?>

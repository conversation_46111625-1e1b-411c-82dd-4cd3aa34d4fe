<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

ini_set('display_errors', '0');
error_reporting(0);

require '../Helper.php';

class SimpleLogAPI {
    private $conn;
    private $helper;

    public function __construct() {
        $this->helper = new Helper();
        try {
            $this->conn = $this->helper->conn();
        } catch (Exception $e) {
            $this->sendError("Database connection failed");
        }
    }

    public function getLogs() {
        $logType = $_GET['type'] ?? 'all';
        $dateFrom = $_GET['date_from'] ?? '';
        $dateTo = $_GET['date_to'] ?? '';
        $status = $_GET['status'] ?? '';
        $search = $_GET['search'] ?? '';
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 50);
        $offset = ($page - 1) * $limit;

        try {
            $result = $this->getLogData($logType, $dateFrom, $dateTo, $status, $search, $limit, $offset);
            
            $this->sendSuccess([
                'data' => $result['logs'],
                'total' => $result['total'],
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($result['total'] / $limit)
            ]);

        } catch (Exception $e) {
            $this->sendError($e->getMessage());
        }
    }

    private function getLogData($logType, $dateFrom, $dateTo, $status, $search, $limit, $offset) {
        // Only get database logs, no file logs
        return $this->getAllLogsFromDatabase($logType, $dateFrom, $dateTo, $status, $search, $limit, $offset);
    }

    private function getAllLogsFromDatabase($logType, $dateFrom, $dateTo, $status, $search, $limit, $offset) {
        // Get logs only from database tables
        $allLogs = [];

        // List of tables to query based on log type
        $tables = [];

        switch ($logType) {
            case 'batal_surat_kontrol':
                $tables = ['log_batal_surat_kontrol' => 'Batal Surat Kontrol'];
                break;
            case 'add_antrean':
                $tables = ['log_add_antrean' => 'Tambah Antrean'];
                break;
            case 'batal_antrean':
                $tables = ['log_batal_antrean' => 'Batal Antrean'];
                break;
            case 'update_antrean':
                $tables = ['log_update_antrean' => 'Update Antrean'];
                break;
            case 'all':
            default:
                $tables = [
                    'log_batal_surat_kontrol' => 'Batal Surat Kontrol',
                    'log_add_antrean' => 'Tambah Antrean',
                    'log_batal_antrean' => 'Batal Antrean',
                    'log_update_antrean' => 'Update Antrean'
                ];
                break;
        }

        // Get logs from each table
        foreach ($tables as $tableName => $logTypeName) {
            try {
                $tableLogs = $this->getTableLogsWithFilter($tableName, $logTypeName, $dateFrom, $dateTo, $status, $search);
                $allLogs = array_merge($allLogs, $tableLogs);
            } catch (Exception $e) {
                error_log("Error getting logs from $tableName: " . $e->getMessage());
                continue;
            }
        }

        // Sort all logs by date descending
        usort($allLogs, function($a, $b) {
            return strtotime($b['tanggal']) - strtotime($a['tanggal']);
        });

        $total = count($allLogs);
        $logs = array_slice($allLogs, $offset, $limit);

        return ['logs' => $logs, 'total' => $total];
    }

    private function getDatabaseLogs($dateFrom, $dateTo, $status, $search) {
        $logs = [];
        
        // List of possible log tables
        $tables = [
            'log_batal_surat_kontrol' => 'Batal Surat Kontrol',
            'log_add_antrean' => 'Tambah Antrean',
            'log_batal_antrean' => 'Batal Antrean',
            'log_update_antrean' => 'Update Antrean'
        ];
        
        foreach ($tables as $tableName => $logType) {
            try {
                $tableLogs = $this->getTableLogs($tableName, $logType, $dateFrom, $dateTo, $status, $search);
                $logs = array_merge($logs, $tableLogs);
            } catch (Exception $e) {
                // Skip this table if there's an error
                error_log("Error getting logs from $tableName: " . $e->getMessage());
                continue;
            }
        }
        
        return $logs;
    }

    private function getTableLogsWithFilter($tableName, $logType, $dateFrom, $dateTo, $status, $search) {
        // Check if table exists
        $checkQuery = "SHOW TABLES LIKE '$tableName'";
        $checkResult = $this->conn->query($checkQuery);
        
        if (!$checkResult || $checkResult->num_rows == 0) {
            return [];
        }
        
        // Get table structure
        $columnsQuery = "SHOW COLUMNS FROM remun_medis.$tableName";
        $columnsResult = $this->conn->query($columnsQuery);
        
        if (!$columnsResult) {
            return [];
        }
        
        $columns = [];
        while ($col = $columnsResult->fetch_assoc()) {
            $columns[] = $col['Field'];
        }
        
        // Build basic query with proper filtering
        $selectCols = ['uuid'];
        if (in_array('request', $columns)) $selectCols[] = 'request';
        if (in_array('response', $columns)) $selectCols[] = 'response';
        if (in_array('code', $columns)) $selectCols[] = 'code';
        if (in_array('suratkontrol', $columns)) $selectCols[] = 'suratkontrol';
        if (in_array('id_perjanjian', $columns)) $selectCols[] = 'id_perjanjian';
        if (in_array('task_id', $columns)) $selectCols[] = 'task_id';

        // Determine date column
        $dateCol = null;
        if (in_array('created_at', $columns)) $dateCol = 'created_at';
        elseif (in_array('waktu', $columns)) $dateCol = 'waktu';
        elseif (in_array('timestamp', $columns)) $dateCol = 'timestamp';

        if ($dateCol) $selectCols[] = $dateCol;

        // Build WHERE conditions with proper escaping
        $whereConditions = [];
        $params = [];
        $types = '';

        // Add date filters if date column exists
        if ($dateCol) {
            if ($dateFrom) {
                $whereConditions[] = "DATE($dateCol) >= ?";
                $params[] = $dateFrom;
                $types .= 's';
            }
            if ($dateTo) {
                $whereConditions[] = "DATE($dateCol) <= ?";
                $params[] = $dateTo;
                $types .= 's';
            }
        }

        // Add status filter
        if ($status && in_array('code', $columns)) {
            $whereConditions[] = "code = ?";
            $params[] = $status;
            $types .= 's';
        }

        // Add search filter
        if ($search) {
            $searchConds = [];
            if (in_array('request', $columns)) {
                $searchConds[] = "request LIKE ?";
                $params[] = "%$search%";
                $types .= 's';
            }
            if (in_array('response', $columns)) {
                $searchConds[] = "response LIKE ?";
                $params[] = "%$search%";
                $types .= 's';
            }
            if (in_array('suratkontrol', $columns)) {
                $searchConds[] = "suratkontrol LIKE ?";
                $params[] = "%$search%";
                $types .= 's';
            }

            if (!empty($searchConds)) {
                $whereConditions[] = "(" . implode(' OR ', $searchConds) . ")";
            }
        }

        // Build final query
        $query = "SELECT " . implode(', ', $selectCols) . " FROM remun_medis.$tableName";

        if (!empty($whereConditions)) {
            $query .= " WHERE " . implode(' AND ', $whereConditions);
        }

        if ($dateCol) {
            $query .= " ORDER BY $dateCol DESC";
        }

        $query .= " LIMIT 1000"; // Increased limit for better filtering
        
        // Execute query with prepared statement if there are parameters
        if (!empty($params)) {
            $stmt = $this->conn->prepare($query);
            if (!$stmt) {
                error_log("Prepare failed for $tableName: " . $this->conn->error);
                return [];
            }

            if (!$stmt->bind_param($types, ...$params)) {
                error_log("Bind param failed for $tableName: " . $stmt->error);
                return [];
            }

            if (!$stmt->execute()) {
                error_log("Execute failed for $tableName: " . $stmt->error);
                return [];
            }

            $result = $stmt->get_result();
        } else {
            $result = $this->conn->query($query);
        }

        if (!$result) {
            error_log("Query failed for $tableName: " . $this->conn->error);
            return [];
        }
        
        $logs = [];
        while ($row = $result->fetch_assoc()) {
            $requestData = isset($row['request']) ? json_decode($row['request'], true) : null;
            $responseData = isset($row['response']) ? json_decode($row['response'], true) : null;
            
            $logs[] = [
                'id' => $row['uuid'],
                'tanggal' => $row[$dateCol] ?? date('Y-m-d H:i:s'),
                'jenis_log' => $logType,
                'status' => $row['code'] ?? 'Unknown',
                'nomor_kartu' => '',
                'nama_pasien' => '',
                'detail' => [
                    'request' => $requestData,
                    'response' => $responseData,
                    'code' => $row['code'] ?? 'Unknown',
                    'suratkontrol' => $row['suratkontrol'] ?? '',
                    'id_perjanjian' => $row['id_perjanjian'] ?? ''
                ]
            ];
        }
        
        return $logs;
    }

    public function getStats() {
        try {
            // Simple stats calculation from database only
            $stats = [
                'total' => 0,
                'success' => 0,
                'error' => 0,
                'today' => 0
            ];

            // Get stats from database tables
            $dbStats = $this->getDatabaseStats();
            $stats['total'] = $dbStats['total'];
            $stats['success'] = $dbStats['success'];
            $stats['error'] = $dbStats['error'];
            $stats['today'] = $dbStats['today'];

            $this->sendSuccess($stats);

        } catch (Exception $e) {
            $this->sendError($e->getMessage());
        }
    }

    private function getDatabaseStats() {
        $stats = ['total' => 0, 'success' => 0, 'error' => 0, 'today' => 0];
        $today = date('Y-m-d');

        $tables = [
            'log_batal_surat_kontrol',
            'log_add_antrean',
            'log_batal_antrean',
            'log_update_antrean'
        ];

        foreach ($tables as $table) {
            try {
                // Check if table exists
                $checkQuery = "SHOW TABLES LIKE '$table'";
                $checkResult = $this->conn->query($checkQuery);

                if (!$checkResult || $checkResult->num_rows == 0) {
                    continue;
                }

                // Get table structure
                $columnsQuery = "SHOW COLUMNS FROM remun_medis.$table";
                $columnsResult = $this->conn->query($columnsQuery);

                if (!$columnsResult) {
                    continue;
                }

                $columns = [];
                while ($col = $columnsResult->fetch_assoc()) {
                    $columns[] = $col['Field'];
                }

                // Determine date column
                $dateCol = null;
                if (in_array('created_at', $columns)) $dateCol = 'created_at';
                elseif (in_array('waktu', $columns)) $dateCol = 'waktu';
                elseif (in_array('timestamp', $columns)) $dateCol = 'timestamp';

                // Count total records
                $totalQuery = "SELECT COUNT(*) as total FROM remun_medis.$table";
                $totalResult = $this->conn->query($totalQuery);
                if ($totalResult) {
                    $stats['total'] += $totalResult->fetch_assoc()['total'];
                }

                // Count success records (status 200, 201)
                if (in_array('code', $columns)) {
                    $successQuery = "SELECT COUNT(*) as success FROM remun_medis.$table WHERE code IN ('200', '201')";
                    $successResult = $this->conn->query($successQuery);
                    if ($successResult) {
                        $stats['success'] += $successResult->fetch_assoc()['success'];
                    }

                    // Count error records (status not 200, 201)
                    $errorQuery = "SELECT COUNT(*) as error FROM remun_medis.$table WHERE code NOT IN ('200', '201')";
                    $errorResult = $this->conn->query($errorQuery);
                    if ($errorResult) {
                        $stats['error'] += $errorResult->fetch_assoc()['error'];
                    }
                }

                // Count today's records
                if ($dateCol) {
                    $todayQuery = "SELECT COUNT(*) as today FROM remun_medis.$table WHERE DATE($dateCol) = '$today'";
                    $todayResult = $this->conn->query($todayQuery);
                    if ($todayResult) {
                        $stats['today'] += $todayResult->fetch_assoc()['today'];
                    }
                }

            } catch (Exception $e) {
                error_log("Error getting stats from $table: " . $e->getMessage());
                continue;
            }
        }

        return $stats;
    }

    public function getLogDetail() {
        $logId = $_GET['id'] ?? '';
        $logType = $_GET['log_type'] ?? '';
        
        if (empty($logId)) {
            $this->sendError('ID log tidak ditemukan');
            return;
        }
        
        try {
            // Only get database log details, no file logs
            $detail = $this->getDatabaseLogDetail($logId);
            
            if ($detail) {
                $this->sendSuccess($detail);
            } else {
                $this->sendError('Detail log tidak ditemukan');
            }
            
        } catch (Exception $e) {
            $this->sendError($e->getMessage());
        }
    }

    private function getSuratKontrolDetail($logId) {
        $logFile = '../log-surat-kontrol.txt';
        
        if (!file_exists($logFile)) {
            return null;
        }
        
        $content = file_get_contents($logFile);
        $lines = explode("\n", $content);
        
        foreach ($lines as $line) {
            if (empty(trim($line))) continue;
            
            if (md5($line) === $logId) {
                if (preg_match('/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (.+)$/', $line, $matches)) {
                    $timestamp = $matches[1];
                    $jsonData = json_decode($matches[2], true);
                    
                    return [
                        'timestamp' => $timestamp,
                        'data' => $jsonData,
                        'code' => $jsonData['response']['metaData']['code'] ?? 'Unknown'
                    ];
                }
            }
        }
        
        return null;
    }

    private function getDatabaseLogDetail($logId) {
        // Simple approach - try to find the log in any table
        $tables = ['log_batal_surat_kontrol', 'log_add_antrean', 'log_batal_antrean', 'log_update_antrean'];
        
        foreach ($tables as $table) {
            try {
                $query = "SELECT * FROM remun_medis.$table WHERE uuid = ?";
                $stmt = $this->conn->prepare($query);
                
                if ($stmt) {
                    $stmt->bind_param('s', $logId);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    
                    if ($result && $row = $result->fetch_assoc()) {
                        $row['request_formatted'] = json_decode($row['request'] ?? '{}', true);
                        $row['response_formatted'] = json_decode($row['response'] ?? '{}', true);
                        return $row;
                    }
                }
            } catch (Exception $e) {
                continue;
            }
        }
        
        return null;
    }

    private function sendSuccess($data) {
        echo json_encode(['success' => true, 'data' => $data]);
        exit;
    }

    private function sendError($message) {
        echo json_encode(['success' => false, 'message' => $message]);
        exit;
    }
}

// Handle requests
$api = new SimpleLogAPI();

$action = $_GET['action'] ?? 'logs';

switch ($action) {
    case 'logs':
        $api->getLogs();
        break;
    case 'stats':
        $api->getStats();
        break;
    case 'detail':
        $api->getLogDetail();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}
?>

<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

ini_set('display_errors', '0');
error_reporting(0);

require '../Helper.php';

class SimpleLogAPI {
    private $conn;
    private $helper;

    public function __construct() {
        $this->helper = new Helper();
        try {
            $this->conn = $this->helper->conn();
        } catch (Exception $e) {
            $this->sendError("Database connection failed");
        }
    }

    public function getLogs() {
        $logType = $_GET['type'] ?? 'all';
        $dateFrom = $_GET['date_from'] ?? '';
        $dateTo = $_GET['date_to'] ?? '';
        $status = $_GET['status'] ?? '';
        $search = $_GET['search'] ?? '';
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 50);
        $offset = ($page - 1) * $limit;

        try {
            $result = $this->getLogData($logType, $dateFrom, $dateTo, $status, $search, $limit, $offset);
            
            $this->sendSuccess([
                'data' => $result['logs'],
                'total' => $result['total'],
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($result['total'] / $limit)
            ]);

        } catch (Exception $e) {
            $this->sendError($e->getMessage());
        }
    }

    private function getLogData($logType, $dateFrom, $dateTo, $status, $search, $limit, $offset) {
        switch ($logType) {
            case 'surat_kontrol':
                return $this->getSuratKontrolFromFile($dateFrom, $dateTo, $status, $search, $limit, $offset);
            case 'all':
            default:
                return $this->getAllLogsSimple($dateFrom, $dateTo, $status, $search, $limit, $offset);
        }
    }

    private function getSuratKontrolFromFile($dateFrom, $dateTo, $status, $search, $limit, $offset) {
        $logs = [];
        $logFile = '../log-surat-kontrol.txt';
        
        if (!file_exists($logFile)) {
            return ['logs' => [], 'total' => 0];
        }
        
        $content = file_get_contents($logFile);
        $lines = explode("\n", $content);
        
        foreach ($lines as $line) {
            if (empty(trim($line))) continue;
            
            // Parse log line: timestamp + JSON
            if (preg_match('/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (.+)$/', $line, $matches)) {
                $timestamp = $matches[1];
                $jsonData = json_decode($matches[2], true);
                
                if ($jsonData) {
                    $logDate = date('Y-m-d', strtotime($timestamp));
                    
                    // Apply filters
                    if ($dateFrom && $logDate < $dateFrom) continue;
                    if ($dateTo && $logDate > $dateTo) continue;
                    if ($status && isset($jsonData['response']['metaData']['code']) && $jsonData['response']['metaData']['code'] != $status) continue;
                    if ($search && stripos(json_encode($jsonData), $search) === false) continue;
                    
                    $logs[] = [
                        'id' => md5($line),
                        'tanggal' => $timestamp,
                        'jenis_log' => 'Surat Kontrol',
                        'status' => $jsonData['response']['metaData']['code'] ?? 'Unknown',
                        'nomor_kartu' => $jsonData['nomorkartu'] ?? '',
                        'nama_pasien' => '',
                        'detail' => $jsonData
                    ];
                }
            }
        }
        
        // Sort by date descending
        usort($logs, function($a, $b) {
            return strtotime($b['tanggal']) - strtotime($a['tanggal']);
        });
        
        $total = count($logs);
        $logs = array_slice($logs, $offset, $limit);
        
        return ['logs' => $logs, 'total' => $total];
    }

    private function getAllLogsSimple($dateFrom, $dateTo, $status, $search, $limit, $offset) {
        // Start with file-based logs
        $allLogs = [];
        
        // Get surat kontrol logs from file
        $suratKontrolLogs = $this->getSuratKontrolFromFile($dateFrom, $dateTo, $status, $search, 1000, 0);
        $allLogs = array_merge($allLogs, $suratKontrolLogs['logs']);
        
        // Try to get database logs with error handling
        try {
            $dbLogs = $this->getDatabaseLogs($dateFrom, $dateTo, $status, $search);
            $allLogs = array_merge($allLogs, $dbLogs);
        } catch (Exception $e) {
            // Continue without database logs if there's an error
            error_log("Database logs error: " . $e->getMessage());
        }
        
        // Sort all logs by date
        usort($allLogs, function($a, $b) {
            return strtotime($b['tanggal']) - strtotime($a['tanggal']);
        });
        
        $total = count($allLogs);
        $logs = array_slice($allLogs, $offset, $limit);
        
        return ['logs' => $logs, 'total' => $total];
    }

    private function getDatabaseLogs($dateFrom, $dateTo, $status, $search) {
        $logs = [];
        
        // List of possible log tables
        $tables = [
            'log_batal_surat_kontrol' => 'Batal Surat Kontrol',
            'log_add_antrean' => 'Tambah Antrean',
            'log_batal_antrean' => 'Batal Antrean',
            'log_update_antrean' => 'Update Antrean'
        ];
        
        foreach ($tables as $tableName => $logType) {
            try {
                $tableLogs = $this->getTableLogs($tableName, $logType, $dateFrom, $dateTo, $status, $search);
                $logs = array_merge($logs, $tableLogs);
            } catch (Exception $e) {
                // Skip this table if there's an error
                error_log("Error getting logs from $tableName: " . $e->getMessage());
                continue;
            }
        }
        
        return $logs;
    }

    private function getTableLogs($tableName, $logType, $dateFrom, $dateTo, $status, $search) {
        // Check if table exists
        $checkQuery = "SHOW TABLES LIKE '$tableName'";
        $checkResult = $this->conn->query($checkQuery);
        
        if (!$checkResult || $checkResult->num_rows == 0) {
            return [];
        }
        
        // Get table structure
        $columnsQuery = "SHOW COLUMNS FROM remun_medis.$tableName";
        $columnsResult = $this->conn->query($columnsQuery);
        
        if (!$columnsResult) {
            return [];
        }
        
        $columns = [];
        while ($col = $columnsResult->fetch_assoc()) {
            $columns[] = $col['Field'];
        }
        
        // Build basic query
        $selectCols = ['uuid'];
        if (in_array('request', $columns)) $selectCols[] = 'request';
        if (in_array('response', $columns)) $selectCols[] = 'response';
        if (in_array('code', $columns)) $selectCols[] = 'code';
        if (in_array('suratkontrol', $columns)) $selectCols[] = 'suratkontrol';
        if (in_array('id_perjanjian', $columns)) $selectCols[] = 'id_perjanjian';
        
        // Determine date column
        $dateCol = null;
        if (in_array('created_at', $columns)) $dateCol = 'created_at';
        elseif (in_array('waktu', $columns)) $dateCol = 'waktu';
        elseif (in_array('timestamp', $columns)) $dateCol = 'timestamp';
        
        if ($dateCol) $selectCols[] = $dateCol;
        
        $query = "SELECT " . implode(', ', $selectCols) . " FROM remun_medis.$tableName";
        
        // Add basic WHERE clause
        $whereConditions = ['1=1'];
        
        // Add date filters if date column exists
        if ($dateCol) {
            if ($dateFrom) {
                $whereConditions[] = "DATE($dateCol) >= '$dateFrom'";
            }
            if ($dateTo) {
                $whereConditions[] = "DATE($dateCol) <= '$dateTo'";
            }
        }
        
        // Add status filter
        if ($status && in_array('code', $columns)) {
            $whereConditions[] = "code = '$status'";
        }
        
        // Add search filter
        if ($search) {
            $searchConds = [];
            if (in_array('request', $columns)) $searchConds[] = "request LIKE '%$search%'";
            if (in_array('response', $columns)) $searchConds[] = "response LIKE '%$search%'";
            if (in_array('suratkontrol', $columns)) $searchConds[] = "suratkontrol LIKE '%$search%'";
            
            if (!empty($searchConds)) {
                $whereConditions[] = "(" . implode(' OR ', $searchConds) . ")";
            }
        }
        
        if (count($whereConditions) > 1) {
            $query .= " WHERE " . implode(' AND ', $whereConditions);
        }
        
        if ($dateCol) {
            $query .= " ORDER BY $dateCol DESC";
        }
        
        $query .= " LIMIT 100"; // Limit to prevent memory issues
        
        $result = $this->conn->query($query);
        
        if (!$result) {
            return [];
        }
        
        $logs = [];
        while ($row = $result->fetch_assoc()) {
            $requestData = isset($row['request']) ? json_decode($row['request'], true) : null;
            $responseData = isset($row['response']) ? json_decode($row['response'], true) : null;
            
            $logs[] = [
                'id' => $row['uuid'],
                'tanggal' => $row[$dateCol] ?? date('Y-m-d H:i:s'),
                'jenis_log' => $logType,
                'status' => $row['code'] ?? 'Unknown',
                'nomor_kartu' => '',
                'nama_pasien' => '',
                'detail' => [
                    'request' => $requestData,
                    'response' => $responseData,
                    'code' => $row['code'] ?? 'Unknown',
                    'suratkontrol' => $row['suratkontrol'] ?? '',
                    'id_perjanjian' => $row['id_perjanjian'] ?? ''
                ]
            ];
        }
        
        return $logs;
    }

    public function getStats() {
        try {
            // Simple stats calculation
            $stats = [
                'total' => 0,
                'success' => 0,
                'error' => 0,
                'today' => 0
            ];
            
            // Get stats from file logs
            $fileStats = $this->getFileStats();
            $stats['total'] += $fileStats['total'];
            $stats['success'] += $fileStats['success'];
            $stats['error'] += $fileStats['error'];
            $stats['today'] += $fileStats['today'];
            
            $this->sendSuccess($stats);
            
        } catch (Exception $e) {
            $this->sendError($e->getMessage());
        }
    }

    private function getFileStats() {
        $stats = ['total' => 0, 'success' => 0, 'error' => 0, 'today' => 0];
        $logFile = '../log-surat-kontrol.txt';
        $today = date('Y-m-d');
        
        if (!file_exists($logFile)) {
            return $stats;
        }
        
        $content = file_get_contents($logFile);
        $lines = explode("\n", $content);
        
        foreach ($lines as $line) {
            if (empty(trim($line))) continue;
            
            if (preg_match('/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (.+)$/', $line, $matches)) {
                $timestamp = $matches[1];
                $jsonData = json_decode($matches[2], true);
                
                if ($jsonData) {
                    $stats['total']++;
                    
                    $code = $jsonData['response']['metaData']['code'] ?? '';
                    if (in_array($code, ['200', '201'])) {
                        $stats['success']++;
                    } else {
                        $stats['error']++;
                    }
                    
                    if (date('Y-m-d', strtotime($timestamp)) === $today) {
                        $stats['today']++;
                    }
                }
            }
        }
        
        return $stats;
    }

    public function getLogDetail() {
        $logId = $_GET['id'] ?? '';
        $logType = $_GET['log_type'] ?? '';
        
        if (empty($logId)) {
            $this->sendError('ID log tidak ditemukan');
            return;
        }
        
        try {
            if ($logType === 'Surat Kontrol') {
                $detail = $this->getSuratKontrolDetail($logId);
            } else {
                $detail = $this->getDatabaseLogDetail($logId);
            }
            
            if ($detail) {
                $this->sendSuccess($detail);
            } else {
                $this->sendError('Detail log tidak ditemukan');
            }
            
        } catch (Exception $e) {
            $this->sendError($e->getMessage());
        }
    }

    private function getSuratKontrolDetail($logId) {
        $logFile = '../log-surat-kontrol.txt';
        
        if (!file_exists($logFile)) {
            return null;
        }
        
        $content = file_get_contents($logFile);
        $lines = explode("\n", $content);
        
        foreach ($lines as $line) {
            if (empty(trim($line))) continue;
            
            if (md5($line) === $logId) {
                if (preg_match('/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (.+)$/', $line, $matches)) {
                    $timestamp = $matches[1];
                    $jsonData = json_decode($matches[2], true);
                    
                    return [
                        'timestamp' => $timestamp,
                        'data' => $jsonData,
                        'code' => $jsonData['response']['metaData']['code'] ?? 'Unknown'
                    ];
                }
            }
        }
        
        return null;
    }

    private function getDatabaseLogDetail($logId) {
        // Simple approach - try to find the log in any table
        $tables = ['log_batal_surat_kontrol', 'log_add_antrean', 'log_batal_antrean', 'log_update_antrean'];
        
        foreach ($tables as $table) {
            try {
                $query = "SELECT * FROM remun_medis.$table WHERE uuid = ?";
                $stmt = $this->conn->prepare($query);
                
                if ($stmt) {
                    $stmt->bind_param('s', $logId);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    
                    if ($result && $row = $result->fetch_assoc()) {
                        $row['request_formatted'] = json_decode($row['request'] ?? '{}', true);
                        $row['response_formatted'] = json_decode($row['response'] ?? '{}', true);
                        return $row;
                    }
                }
            } catch (Exception $e) {
                continue;
            }
        }
        
        return null;
    }

    private function sendSuccess($data) {
        echo json_encode(['success' => true, 'data' => $data]);
        exit;
    }

    private function sendError($message) {
        echo json_encode(['success' => false, 'message' => $message]);
        exit;
    }
}

// Handle requests
$api = new SimpleLogAPI();

$action = $_GET['action'] ?? 'logs';

switch ($action) {
    case 'logs':
        $api->getLogs();
        break;
    case 'stats':
        $api->getStats();
        break;
    case 'detail':
        $api->getLogDetail();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}
?>

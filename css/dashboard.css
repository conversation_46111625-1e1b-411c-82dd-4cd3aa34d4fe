/* Dashboard Custom Styles */

/* Sidebar Enhancements */
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    overflow-y: auto;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8) !important;
    transition: all 0.3s ease;
    margin: 2px 0;
    border-radius: 8px;
    padding: 12px 16px;
}

.sidebar .nav-link:hover {
    color: white !important;
    background-color: rgba(255,255,255,0.15);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: white !important;
    background-color: rgba(255,255,255,0.2);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.sidebar .nav-link i {
    width: 20px;
    margin-right: 10px;
}

/* Main Content */
.main-content {
    margin-left: 250px;
    padding: 20px;
    min-height: 100vh;
    background-color: #f8f9fa;
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
    padding: 20px;
}

.card-body {
    padding: 20px;
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stats-card .card-body {
    position: relative;
    z-index: 2;
}

.bg-primary.stats-card {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.bg-success.stats-card {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.bg-danger.stats-card {
    background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%) !important;
}

.bg-info.stats-card {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
}

/* Filter Section */
.filter-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.05);
    border: 1px solid #e9ecef;
}

.filter-section h5 {
    color: #495057;
    margin-bottom: 20px;
    font-weight: 600;
}

.filter-section .form-control,
.filter-section .form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.filter-section .form-control:focus,
.filter-section .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Table Styles */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    border: none;
    padding: 15px 12px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85em;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table tbody td {
    padding: 15px 12px;
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

/* Status Badges */
.status-badge {
    font-size: 0.8em;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Buttons */
.btn {
    border-radius: 8px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-outline-primary {
    border-color: #007bff;
    color: #007bff;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
}

/* Modal Enhancements */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0;
    padding: 20px 25px;
}

.modal-body {
    padding: 25px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-body pre {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.85em;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 15px;
    }
    
    .filter-section {
        padding: 20px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .table-responsive {
        font-size: 0.9em;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .stats-card .card-body {
        padding: 15px;
    }
}

@media (max-width: 576px) {
    .filter-section .row .col-md-3 {
        margin-bottom: 15px;
    }
    
    .table thead th,
    .table tbody td {
        padding: 10px 8px;
        font-size: 0.85em;
    }
    
    .btn {
        padding: 6px 15px;
        font-size: 0.9em;
    }
    
    .modal-dialog {
        margin: 10px;
    }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Pagination */
.pagination {
    margin: 0;
}

.page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #495057;
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
    transform: translateY(-1px);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 2px 15px rgba(0,0,0,0.08) !important;
}

.border-radius-lg {
    border-radius: 15px !important;
}

.border-radius-xl {
    border-radius: 20px !important;
}

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Log BPJS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white"><i class="fas fa-chart-line"></i> Dashboard Log</h4>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" data-log-type="all">
                                <i class="fas fa-list"></i> Semua Log
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-log-type="surat_kontrol">
                                <i class="fas fa-file-medical"></i> Log Surat Kontrol
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-log-type="batal_surat_kontrol">
                                <i class="fas fa-times-circle"></i> Log Batal Surat Kontrol
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-log-type="add_antrean">
                                <i class="fas fa-plus-circle"></i> Log Tambah Antrean
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-log-type="batal_antrean">
                                <i class="fas fa-ban"></i> Log Batal Antrean
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-log-type="update_antrean">
                                <i class="fas fa-edit"></i> Log Update Antrean
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-outline-secondary d-md-none me-3" type="button" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="h2 text-gradient">Dashboard Log BPJS</h1>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Filter Section -->
                <div class="filter-section">
                    <h5><i class="fas fa-filter"></i> Filter Data</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <label for="dateFrom" class="form-label">Dari Tanggal</label>
                            <input type="date" class="form-control" id="dateFrom">
                        </div>
                        <div class="col-md-3">
                            <label for="dateTo" class="form-label">Sampai Tanggal</label>
                            <input type="date" class="form-control" id="dateTo">
                        </div>
                        <div class="col-md-3">
                            <label for="statusFilter" class="form-label">Status</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">Semua Status</option>
                                <option value="200">Sukses (200)</option>
                                <option value="201">Created (201)</option>
                                <option value="203">Error 203</option>
                                <option value="204">Error 204</option>
                                <option value="400">Bad Request (400)</option>
                                <option value="500">Server Error (500)</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="searchText" class="form-label">Pencarian</label>
                            <input type="text" class="form-control" id="searchText" placeholder="Cari nomor kartu, nama...">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="button" class="btn btn-primary" onclick="applyFilter()">
                                <i class="fas fa-search"></i> Terapkan Filter
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetFilter()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4 fade-in" id="statsCards">
                    <div class="col-md-3 mb-3">
                        <div class="card text-white bg-primary stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title" id="totalLogs">0</h4>
                                        <p class="card-text">Total Log</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-list fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card text-white bg-success stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title" id="successLogs">0</h4>
                                        <p class="card-text">Sukses</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card text-white bg-danger stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title" id="errorLogs">0</h4>
                                        <p class="card-text">Error</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exclamation-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card text-white bg-info stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title" id="todayLogs">0</h4>
                                        <p class="card-text">Hari Ini</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar-day fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0" id="tableTitle">Data Log</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="logTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>No</th>
                                        <th>Tanggal</th>
                                        <th>Jenis Log</th>
                                        <th>Status</th>
                                        <th>Nomor Kartu</th>
                                        <th>Nama Pasien</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="logTableBody">
                                    <!-- Data akan dimuat via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal Detail Log -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Detail Log</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- Detail akan dimuat via AJAX -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="js/dashboard_fixed.js"></script>
</body>
</html>

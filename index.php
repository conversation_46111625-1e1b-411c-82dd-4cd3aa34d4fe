<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Log BPJS - Home</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .welcome-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .logo {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        .btn-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            color: white;
        }
        .feature-list {
            text-align: left;
            margin: 30px 0;
        }
        .feature-item {
            margin: 10px 0;
            color: #666;
        }
        .feature-item i {
            color: #667eea;
            width: 20px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="welcome-card">
        <div class="logo">
            <i class="fas fa-chart-line"></i>
        </div>
        <h1 class="h2 mb-3">Dashboard Log BPJS</h1>
        <p class="text-muted mb-4">
            Sistem monitoring dan analisis log untuk operasi BPJS
        </p>
        
        <div class="feature-list">
            <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                Monitor log surat kontrol dan antrean
            </div>
            <div class="feature-item">
                <i class="fas fa-filter"></i>
                Filter dan pencarian data yang fleksibel
            </div>
            <div class="feature-item">
                <i class="fas fa-chart-bar"></i>
                Statistik real-time dan analisis
            </div>
            <div class="feature-item">
                <i class="fas fa-mobile-alt"></i>
                Tampilan responsif untuk semua perangkat
            </div>
            <div class="feature-item">
                <i class="fas fa-eye"></i>
                Detail log lengkap dengan format JSON
            </div>
        </div>

        <div class="mt-4">
            <a href="dashboard.php" class="btn-custom">
                <i class="fas fa-tachometer-alt"></i>
                Buka Dashboard
            </a>
            <a href="demo.php" class="btn-custom">
                <i class="fas fa-play"></i>
                Lihat Demo
            </a>
        </div>

        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                Pastikan koneksi database sudah dikonfigurasi dengan benar
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

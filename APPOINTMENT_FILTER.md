# Filter Tanggal Perjanjian - IMPLEMENTED! 📅

## ✅ Fitur Baru: Filter Berdasarkan Tanggal Perjanjian

### 🎯 **Apa yang Ditambahkan:**

Filter baru yang memungkinkan pencarian log berdasarkan **tanggal perjanjian** yang tersimpan dalam data JSON request, bukan hanya tanggal log dibuat.

### 🔧 **Implementasi Teknis:**

#### 1. **API Parameter Baru**
```php
// Parameter baru di API
$appointmentDateFrom = $_GET['appointment_date_from'] ?? '';
$appointmentDateTo = $_GET['appointment_date_to'] ?? '';
```

#### 2. **Filter JSON di Database**
```php
// Filter berdasarkan tglRencanaKontrol dalam JSON request
if ($appointmentDateFrom) {
    $whereConditions[] = "JSON_EXTRACT(request, '$.tglRencanaKontrol') >= ?";
    $params[] = $appointmentDateFrom;
}
if ($appointmentDateTo) {
    $whereConditions[] = "JSON_EXTRACT(request, '$.tglRencanaKontrol') <= ?";
    $params[] = $appointmentDateTo;
}
```

#### 3. **UI Dashboard Diperbaharui**
```html
<!-- Field baru di dashboard -->
<input type="date" class="form-control" id="appointmentDateFrom">
<input type="date" class="form-control" id="appointmentDateTo">
```

#### 4. **JavaScript Parameter**
```javascript
const params = {
    // ... parameter lain
    appointment_date_from: $('#appointmentDateFrom').val(),
    appointment_date_to: $('#appointmentDateTo').val(),
};
```

## 🎨 **Tampilan Dashboard Baru:**

### **Filter Section yang Diperbaharui:**
```
┌─────────────────────────────────────────────────────────────┐
│ Filter Data                                                 │
├─────────────────┬─────────────────┬─────────────────────────┤
│ Dari Tgl Log    │ Sampai Tgl Log  │ Dari Tgl Perjanjian    │
│ [date input]    │ [date input]    │ [date input]            │
├─────────────────┼─────────────────┼─────────────────────────┤
│ Sampai Tgl Perjanjian │ Status     │ Pencarian              │
│ [date input]    │ [dropdown]      │ [text input]            │
└─────────────────┴─────────────────┴─────────────────────────┘
│ [Terapkan Filter] [Reset]                                   │
└─────────────────────────────────────────────────────────────┘
```

## 🔍 **Cara Kerja Filter:**

### **1. Filter Tanggal Log (Existing)**
- Menggunakan kolom `created_at`, `waktu`, atau `timestamp`
- Filter berdasarkan kapan log dibuat di database

### **2. Filter Tanggal Perjanjian (NEW)**
- Menggunakan `JSON_EXTRACT(request, '$.tglRencanaKontrol')`
- Filter berdasarkan tanggal perjanjian dalam data request
- Contoh data JSON: `{"tglRencanaKontrol": "2024-01-15"}`

### **3. Kombinasi Filter**
Bisa menggunakan kedua filter bersamaan:
- Log dibuat antara 1-31 Januari 2024
- DAN perjanjian dijadwalkan antara 15-20 Januari 2024

## 🧪 **Testing:**

### **1. `test_appointment_filter.php` - ⭐ RECOMMENDED**
```
http://your-domain/bpjs/test_appointment_filter.php
```
- Test khusus filter tanggal perjanjian
- Quick test buttons untuk berbagai skenario
- Visual response API

### **2. Dashboard Production**
```
http://your-domain/bpjs/dashboard.php
```
- Filter tanggal perjanjian sudah terintegrasi
- Bisa dikombinasi dengan filter lain

## 🎯 **Contoh Penggunaan:**

### **Skenario 1: Cari Perjanjian Minggu Ini**
```
Dari Tanggal Perjanjian: 2024-01-15
Sampai Tanggal Perjanjian: 2024-01-21
```

### **Skenario 2: Log Dibuat Kemarin, Perjanjian Besok**
```
Dari Tanggal Log: 2024-01-14
Sampai Tanggal Log: 2024-01-14
Dari Tanggal Perjanjian: 2024-01-16
Sampai Tanggal Perjanjian: 2024-01-16
```

### **Skenario 3: Kombinasi dengan Status**
```
Dari Tanggal Perjanjian: 2024-01-01
Sampai Tanggal Perjanjian: 2024-01-31
Status: 200 (Sukses)
```

## 📊 **API Endpoints:**

### **Filter Tanggal Perjanjian Saja**
```
api/logs_simple.php?appointment_date_from=2024-01-15&appointment_date_to=2024-01-21
```

### **Filter Kombinasi**
```
api/logs_simple.php?date_from=2024-01-01&date_to=2024-01-31&appointment_date_from=2024-01-15&appointment_date_to=2024-01-21&status=200
```

### **Filter dengan Jenis Log**
```
api/logs_simple.php?type=add_antrean&appointment_date_from=2024-01-15&appointment_date_to=2024-01-21
```

## 🔍 **Data JSON yang Difilter:**

### **Contoh Request JSON:**
```json
{
  "noSEP": "0904R0080624V015921",
  "kodeDokter": "125961",
  "poliKontrol": "URO",
  "tglRencanaKontrol": "2024-01-15",  ← Field yang difilter
  "user": "1065"
}
```

### **SQL Query yang Dihasilkan:**
```sql
SELECT * FROM remun_medis.log_add_antrean 
WHERE JSON_EXTRACT(request, '$.tglRencanaKontrol') >= '2024-01-15'
  AND JSON_EXTRACT(request, '$.tglRencanaKontrol') <= '2024-01-21'
```

## ⚡ **Performance Notes:**

### **Index Recommendation:**
```sql
-- Untuk performance yang lebih baik, buat index pada JSON field
ALTER TABLE remun_medis.log_add_antrean 
ADD INDEX idx_appointment_date ((JSON_EXTRACT(request, '$.tglRencanaKontrol')));
```

### **Limitations:**
- Filter hanya bekerja jika data JSON memiliki field `tglRencanaKontrol`
- Performance tergantung pada ukuran tabel dan index
- JSON_EXTRACT memerlukan MySQL 5.7+ atau MariaDB 10.2+

## 🎯 **Use Cases:**

### **1. Monitoring Perjanjian Hari Ini**
Filter perjanjian yang dijadwalkan hari ini untuk monitoring

### **2. Analisis Perjanjian Periode Tertentu**
Lihat semua log perjanjian dalam rentang waktu tertentu

### **3. Troubleshooting Perjanjian Bermasalah**
Kombinasi filter tanggal perjanjian + status error

### **4. Laporan Bulanan**
Filter perjanjian per bulan untuk laporan

## 🔧 **Troubleshooting:**

### **Filter Tidak Berfungsi:**
1. Pastikan data JSON memiliki field `tglRencanaKontrol`
2. Cek format tanggal (YYYY-MM-DD)
3. Periksa console browser untuk error

### **Performance Lambat:**
1. Tambahkan index pada JSON field
2. Batasi rentang tanggal
3. Kombinasi dengan filter lain untuk mengurangi data

### **Data Tidak Muncul:**
1. Cek apakah tabel memiliki kolom `request`
2. Pastikan data JSON valid
3. Test dengan `test_appointment_filter.php`

---

**Filter Tanggal Perjanjian sekarang aktif! 🎉**

**Key Features:**
- ✅ Filter berdasarkan `tglRencanaKontrol` dalam JSON
- ✅ Kombinasi dengan filter tanggal log
- ✅ Terintegrasi dengan filter lain (status, search, dll)
- ✅ UI yang user-friendly
- ✅ Testing tools tersedia

Test dengan `test_appointment_filter.php` untuk verifikasi!

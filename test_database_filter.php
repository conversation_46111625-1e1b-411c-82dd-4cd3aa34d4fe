<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Database Filter</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background: #f8f9fa; }
        .test-section { margin: 20px 0; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .result.success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .result.error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .result.info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .api-response { background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-database"></i> Test Database Filter</h1>
        
        <!-- Filter Test Section -->
        <div class="test-section">
            <h3><i class="fas fa-filter"></i> Filter Tests</h3>
            <p>Test filter functionality dengan database logs (tanpa file logs)</p>
            
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary w-100 mb-2" onclick="testNoFilter()">
                        <i class="fas fa-list"></i> Test Tanpa Filter
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-success w-100 mb-2" onclick="testDateFilter()">
                        <i class="fas fa-calendar"></i> Test Filter Tanggal
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-info w-100 mb-2" onclick="testStatusFilter()">
                        <i class="fas fa-check-circle"></i> Test Filter Status
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-warning w-100 mb-2" onclick="testSearchFilter()">
                        <i class="fas fa-search"></i> Test Filter Search
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-secondary w-100 mb-2" onclick="testLogTypeFilter()">
                        <i class="fas fa-tags"></i> Test Filter Jenis Log
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-dark w-100 mb-2" onclick="testCombinedFilter()">
                        <i class="fas fa-layer-group"></i> Test Filter Kombinasi
                    </button>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> Test Results</h3>
            <div id="testResults">
                <div class="result info">
                    <i class="fas fa-info-circle"></i> Klik tombol test untuk memulai...
                </div>
            </div>
        </div>

        <!-- API Response Section -->
        <div class="test-section">
            <h3><i class="fas fa-code"></i> API Response</h3>
            <div class="api-response" id="apiResponse">
                Response API akan muncul di sini...
            </div>
        </div>

        <!-- Database Stats -->
        <div class="test-section">
            <h3><i class="fas fa-chart-pie"></i> Database Stats</h3>
            <button class="btn btn-primary" onclick="testStats()">
                <i class="fas fa-play"></i> Test Stats API
            </button>
            <div id="statsResults" class="mt-3"></div>
        </div>

        <!-- Navigation -->
        <div class="test-section">
            <h3><i class="fas fa-compass"></i> Navigation</h3>
            <div class="row">
                <div class="col-md-3">
                    <a href="dashboard.php" class="btn btn-success w-100">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="test_filter.php" class="btn btn-info w-100">
                        <i class="fas fa-vial"></i> Test Filter
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="demo_filter.php" class="btn btn-warning w-100">
                        <i class="fas fa-play"></i> Demo Filter
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="index.php" class="btn btn-secondary w-100">
                        <i class="fas fa-home"></i> Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function addResult(type, message) {
            const icon = type === 'success' ? 'fa-check' : 
                        type === 'error' ? 'fa-times' : 'fa-info-circle';
            
            const html = `
                <div class="result ${type}">
                    <i class="fas ${icon}"></i> ${message}
                </div>
            `;
            $('#testResults').append(html);
        }

        function showApiResponse(response) {
            $('#apiResponse').html(JSON.stringify(response, null, 2));
        }

        function testAPI(params, testName) {
            addResult('info', `Testing: ${testName}`);
            
            $.ajax({
                url: 'api/logs_simple.php',
                method: 'GET',
                data: params,
                dataType: 'json',
                success: function(response) {
                    showApiResponse(response);
                    
                    if (response.success) {
                        const data = response.data;
                        addResult('success', `${testName} - Success! Total: ${data.total}, Displayed: ${data.data ? data.data.length : 0}`);
                        
                        if (data.data && data.data.length > 0) {
                            addResult('info', `Sample data: ${data.data[0].jenis_log} - ${data.data[0].status} - ${data.data[0].tanggal}`);
                        }
                    } else {
                        addResult('error', `${testName} - Error: ${response.message}`);
                    }
                },
                error: function(xhr, status, error) {
                    addResult('error', `${testName} - AJAX Error: ${error}`);
                    showApiResponse({error: error, status: status});
                }
            });
        }

        function testNoFilter() {
            const params = {
                type: 'all',
                page: 1,
                limit: 10
            };
            testAPI(params, 'No Filter (All Data)');
        }

        function testDateFilter() {
            const params = {
                type: 'all',
                page: 1,
                limit: 10,
                date_from: '2024-01-01',
                date_to: '2024-12-31'
            };
            testAPI(params, 'Date Filter (2024)');
        }

        function testStatusFilter() {
            const params = {
                type: 'all',
                page: 1,
                limit: 10,
                status: '200'
            };
            testAPI(params, 'Status Filter (200)');
        }

        function testSearchFilter() {
            const params = {
                type: 'all',
                page: 1,
                limit: 10,
                search: 'test'
            };
            testAPI(params, 'Search Filter (test)');
        }

        function testLogTypeFilter() {
            const params = {
                type: 'add_antrean',
                page: 1,
                limit: 10
            };
            testAPI(params, 'Log Type Filter (add_antrean)');
        }

        function testCombinedFilter() {
            const params = {
                type: 'all',
                page: 1,
                limit: 5,
                date_from: '2024-01-01',
                date_to: '2024-12-31',
                status: '200',
                search: ''
            };
            testAPI(params, 'Combined Filter (Date + Status)');
        }

        function testStats() {
            addResult('info', 'Testing Stats API...');
            
            $.ajax({
                url: 'api/logs_simple.php?action=stats',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const stats = response.data;
                        $('#statsResults').html(`
                            <div class="result success">
                                <i class="fas fa-check"></i> Stats API Success!<br>
                                <strong>Total:</strong> ${stats.total}<br>
                                <strong>Success:</strong> ${stats.success}<br>
                                <strong>Error:</strong> ${stats.error}<br>
                                <strong>Today:</strong> ${stats.today}
                            </div>
                        `);
                    } else {
                        $('#statsResults').html(`
                            <div class="result error">
                                <i class="fas fa-times"></i> Stats API Error: ${response.message}
                            </div>
                        `);
                    }
                },
                error: function() {
                    $('#statsResults').html(`
                        <div class="result error">
                            <i class="fas fa-times"></i> Stats API Connection Error
                        </div>
                    `);
                }
            });
        }

        // Auto test on page load
        $(document).ready(function() {
            addResult('info', 'Database Filter Test initialized');
            addResult('info', 'Note: This test only uses database logs, no file logs');
            
            // Auto run basic test
            setTimeout(() => {
                testNoFilter();
            }, 1000);
        });
    </script>
</body>
</html>

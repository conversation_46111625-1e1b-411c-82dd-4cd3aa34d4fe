<?php
/**
 * Test file untuk API Dashboard Log BPJS
 * File ini untuk testing API tanpa frontend
 */

ini_set('display_errors', '1');
ini_set('display_startup_errors', '1');
error_reporting(E_ALL);

echo "<h1>Test API Dashboard Log BPJS</h1>";

// Test 1: Test koneksi database
echo "<h2>1. Test Koneksi Database</h2>";
try {
    require 'Helper.php';
    $helper = new Helper();
    $conn = $helper->conn();
    echo "<p style='color: green;'>✓ Koneksi database berhasil</p>";
    
    // Test query sederhana
    $result = $conn->query("SELECT 1 as test");
    if ($result) {
        echo "<p style='color: green;'>✓ Query test berhasil</p>";
    } else {
        echo "<p style='color: red;'>✗ Query test gagal: " . $conn->error . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Koneksi database gagal: " . $e->getMessage() . "</p>";
}

// Test 2: Test file log
echo "<h2>2. Test File Log</h2>";
$logFile = 'log-surat-kontrol.txt';
if (file_exists($logFile)) {
    $fileSize = filesize($logFile);
    echo "<p style='color: green;'>✓ File log ditemukan: $logFile (Size: " . formatBytes($fileSize) . ")</p>";
    
    // Test baca beberapa baris
    $content = file_get_contents($logFile);
    $lines = explode("\n", $content);
    $validLines = 0;
    
    foreach (array_slice($lines, 0, 10) as $line) {
        if (empty(trim($line))) continue;
        
        if (preg_match('/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (.+)$/', $line, $matches)) {
            $jsonData = json_decode($matches[2], true);
            if ($jsonData) {
                $validLines++;
            }
        }
    }
    
    echo "<p style='color: green;'>✓ Format log valid, ditemukan $validLines baris valid dari 10 baris pertama</p>";
    
} else {
    echo "<p style='color: orange;'>⚠ File log tidak ditemukan: $logFile</p>";
}

// Test 3: Test API Simple
echo "<h2>3. Test API Simple</h2>";

// Test stats
echo "<h3>3.1 Test Stats API</h3>";
$statsUrl = "api/logs_simple.php?action=stats";
$statsResponse = testApiCall($statsUrl);
if ($statsResponse) {
    echo "<p style='color: green;'>✓ Stats API berhasil</p>";
    echo "<pre>" . json_encode($statsResponse, JSON_PRETTY_PRINT) . "</pre>";
} else {
    echo "<p style='color: red;'>✗ Stats API gagal</p>";
}

// Test logs
echo "<h3>3.2 Test Logs API</h3>";
$logsUrl = "api/logs_simple.php?type=all&limit=5";
$logsResponse = testApiCall($logsUrl);
if ($logsResponse) {
    echo "<p style='color: green;'>✓ Logs API berhasil</p>";
    echo "<p>Total data: " . ($logsResponse['data']['total'] ?? 0) . "</p>";
    echo "<p>Data yang ditampilkan: " . count($logsResponse['data']['data'] ?? []) . "</p>";
} else {
    echo "<p style='color: red;'>✗ Logs API gagal</p>";
}

// Test 4: Test tabel database
echo "<h2>4. Test Tabel Database</h2>";
if (isset($conn)) {
    $tables = [
        'log_batal_surat_kontrol',
        'log_add_antrean', 
        'log_batal_antrean',
        'log_update_antrean'
    ];
    
    foreach ($tables as $table) {
        $checkQuery = "SHOW TABLES LIKE '$table'";
        $result = $conn->query($checkQuery);
        
        if ($result && $result->num_rows > 0) {
            // Table exists, check row count
            $countQuery = "SELECT COUNT(*) as total FROM remun_medis.$table";
            $countResult = $conn->query($countQuery);
            
            if ($countResult) {
                $count = $countResult->fetch_assoc()['total'];
                echo "<p style='color: green;'>✓ Tabel $table ditemukan ($count baris)</p>";
            } else {
                echo "<p style='color: orange;'>⚠ Tabel $table ditemukan tapi tidak bisa dihitung</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ Tabel $table tidak ditemukan</p>";
        }
    }
}

// Test 5: Test permissions
echo "<h2>5. Test Permissions</h2>";

// Check if API files are accessible
$apiFiles = ['api/logs.php', 'api/logs_simple.php'];
foreach ($apiFiles as $file) {
    if (file_exists($file) && is_readable($file)) {
        echo "<p style='color: green;'>✓ File $file dapat diakses</p>";
    } else {
        echo "<p style='color: red;'>✗ File $file tidak dapat diakses</p>";
    }
}

// Check if CSS/JS files exist
$staticFiles = ['css/dashboard.css', 'js/dashboard.js'];
foreach ($staticFiles as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ File $file ditemukan</p>";
    } else {
        echo "<p style='color: red;'>✗ File $file tidak ditemukan</p>";
    }
}

echo "<h2>6. Kesimpulan</h2>";
echo "<p>Jika semua test di atas menunjukkan status ✓ (hijau), maka dashboard siap digunakan.</p>";
echo "<p>Jika ada status ✗ (merah), periksa konfigurasi yang bermasalah.</p>";
echo "<p>Status ⚠ (orange) menunjukkan fitur opsional yang tidak tersedia.</p>";

echo "<hr>";
echo "<p><a href='dashboard.php'>Buka Dashboard</a> | <a href='demo.php'>Buka Demo</a> | <a href='index.php'>Kembali ke Home</a></p>";

// Helper functions
function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}

function testApiCall($url) {
    $fullUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/" . $url;
    
    // Use cURL if available
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $fullUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200 && $response) {
            return json_decode($response, true);
        }
    } else {
        // Fallback to file_get_contents
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        $response = @file_get_contents($fullUrl, false, $context);
        if ($response) {
            return json_decode($response, true);
        }
    }
    
    return false;
}
?>

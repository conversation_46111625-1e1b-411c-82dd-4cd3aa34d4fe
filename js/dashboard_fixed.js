// Global variables
let currentLogType = 'all';
let currentPage = 1;
let dataTable = null;

// Utility functions
function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function formatDateTime(dateTimeStr) {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('id-ID', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

function getStatusBadge(status) {
    let badgeClass = 'bg-secondary';
    let statusText = status;
    
    switch (status) {
        case '200':
        case '201':
            badgeClass = 'bg-success';
            statusText = 'Sukses (' + status + ')';
            break;
        case '203':
        case '204':
        case '400':
        case '404':
            badgeClass = 'bg-warning';
            statusText = 'Warning (' + status + ')';
            break;
        case '500':
            badgeClass = 'bg-danger';
            statusText = 'Error (' + status + ')';
            break;
    }
    
    return `<span class="badge status-badge ${badgeClass}">${statusText}</span>`;
}

// Core functions
function loadStats() {
    $.ajax({
        url: 'api/logs_simple.php?action=stats',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#totalLogs').text(response.data.total || 0);
                $('#successLogs').text(response.data.success || 0);
                $('#errorLogs').text(response.data.error || 0);
                $('#todayLogs').text(response.data.today || 0);
            } else {
                console.error('Stats error:', response.message);
                // Set default values on error
                $('#totalLogs').text('0');
                $('#successLogs').text('0');
                $('#errorLogs').text('0');
                $('#todayLogs').text('0');
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load stats:', error);
            // Set default values on error
            $('#totalLogs').text('0');
            $('#successLogs').text('0');
            $('#errorLogs').text('0');
            $('#todayLogs').text('0');
        }
    });
}

function loadLogs() {
    const params = {
        type: currentLogType,
        page: currentPage,
        limit: 50,
        date_from: $('#dateFrom').val(),
        date_to: $('#dateTo').val(),
        appointment_date_from: $('#appointmentDateFrom').val(),
        appointment_date_to: $('#appointmentDateTo').val(),
        status: $('#statusFilter').val(),
        search: $('#searchText').val()
    };

    // Debug: Log parameters to console
    console.log('Loading logs with parameters:', params);

    // Show loading
    $('#logTableBody').html('<tr><td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat data...</td></tr>');

    $.ajax({
        url: 'api/logs_simple.php',
        method: 'GET',
        data: params,
        dataType: 'json',
        success: function(response) {
            console.log('API Response:', response);
            if (response.success) {
                renderLogTable(response.data.data || []);
                updatePagination(response.data);
            } else {
                $('#logTableBody').html('<tr><td colspan="7" class="text-center text-danger">Error: ' + response.message + '</td></tr>');
            }
        },
        error: function(xhr, status, error) {
            console.error('API Error:', error);
            $('#logTableBody').html('<tr><td colspan="7" class="text-center text-danger">Gagal memuat data</td></tr>');
        }
    });
}

function renderLogTable(logs) {
    let html = '';
    
    // Ensure logs is an array
    if (!Array.isArray(logs)) {
        console.error('Logs is not an array:', logs);
        logs = [];
    }
    
    if (logs.length === 0) {
        html = '<tr><td colspan="7" class="text-center">Tidak ada data</td></tr>';
    } else {
        logs.forEach(function(log, index) {
            const statusBadge = getStatusBadge(log.status);
            const rowNumber = ((currentPage - 1) * 50) + index + 1;
            
            html += `
                <tr>
                    <td>${rowNumber}</td>
                    <td>${formatDateTime(log.tanggal)}</td>
                    <td><span class="badge bg-info">${log.jenis_log}</span></td>
                    <td>${statusBadge}</td>
                    <td>${log.nomor_kartu || '-'}</td>
                    <td>${log.nama_pasien || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="showDetail('${log.id}', '${log.jenis_log}')">
                            <i class="fas fa-eye"></i> Detail
                        </button>
                    </td>
                </tr>
            `;
        });
    }
    
    $('#logTableBody').html(html);
}

function updatePagination(response) {
    // Simple pagination info
    const page = response.page || 1;
    const totalPages = response.total_pages || 1;
    const total = response.total || 0;
    
    const info = `Halaman ${page} dari ${totalPages} (Total: ${total} data)`;
    
    // Add pagination info to card footer if not exists
    if ($('.card-footer').length === 0) {
        $('.card').append('<div class="card-footer d-flex justify-content-between align-items-center"></div>');
    }
    
    $('.card-footer').html(`
        <span class="text-muted">${info}</span>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changePage(${page - 1})" ${page <= 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i> Sebelumnya
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changePage(${page + 1})" ${page >= totalPages ? 'disabled' : ''}>
                Selanjutnya <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    `);
}

function updateTableTitle() {
    const titles = {
        'all': 'Semua Log',
        'surat_kontrol': 'Log Surat Kontrol',
        'batal_surat_kontrol': 'Log Batal Surat Kontrol',
        'add_antrean': 'Log Tambah Antrean',
        'batal_antrean': 'Log Batal Antrean',
        'update_antrean': 'Log Update Antrean'
    };
    
    $('#tableTitle').text(titles[currentLogType] || 'Data Log');
}

function showDetail(logId, logType) {
    // Show loading in modal
    $('#modalBody').html(`
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">Memuat detail log...</p>
        </div>
    `);
    
    $('#detailModal').modal('show');
    
    // Fetch detail data
    $.ajax({
        url: 'api/logs_simple.php?action=detail',
        method: 'GET',
        data: {
            id: logId,
            log_type: logType
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                renderLogDetail(response.data, logType);
            } else {
                $('#modalBody').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Error: ${response.message}
                    </div>
                `);
            }
        },
        error: function() {
            $('#modalBody').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Gagal memuat detail log
                </div>
            `);
        }
    });
}

function renderLogDetail(data, logType) {
    let html = '';
    
    // Header info
    html += `
        <div class="row mb-3">
            <div class="col-md-6">
                <strong>Jenis Log:</strong> <span class="badge bg-info">${logType}</span>
            </div>
            <div class="col-md-6">
                <strong>Status:</strong> ${getStatusBadge(data.code)}
            </div>
        </div>
    `;
    
    // Basic info based on log type
    if (logType === 'Surat Kontrol') {
        html += renderSuratKontrolDetail(data);
    } else {
        html += renderDatabaseLogDetail(data);
    }
    
    $('#modalBody').html(html);
}

function renderSuratKontrolDetail(data) {
    const logData = data.data;
    
    return `
        <div class="row mb-3">
            <div class="col-md-6">
                <strong>Timestamp:</strong> ${formatDateTime(data.timestamp)}
            </div>
            <div class="col-md-6">
                <strong>Nomor Kartu:</strong> ${logData.nomorkartu || '-'}
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-12">
                <strong>Catatan:</strong>
                <ul class="list-unstyled ms-3">
                    ${logData.catatan ? logData.catatan.map(cat => `<li>• ${cat}</li>`).join('') : '<li>-</li>'}
                </ul>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h6>Request:</h6>
                <pre class="bg-light p-2 rounded" style="max-height: 200px; overflow-y: auto;">${JSON.stringify(logData.request, null, 2)}</pre>
            </div>
            <div class="col-md-6">
                <h6>Response:</h6>
                <pre class="bg-light p-2 rounded" style="max-height: 200px; overflow-y: auto;">${JSON.stringify(logData.response, null, 2)}</pre>
            </div>
        </div>
    `;
}

function renderDatabaseLogDetail(data) {
    let html = `
        <div class="row mb-3">
            <div class="col-md-4">
                <strong>UUID:</strong> ${data.uuid}
            </div>
            <div class="col-md-4">
                <strong>Waktu:</strong> ${formatDateTime(data.created_at || data.waktu)}
            </div>
            <div class="col-md-4">
                <strong>Code:</strong> ${getStatusBadge(data.code)}
            </div>
        </div>
    `;
    
    // Add specific fields based on log type
    if (data.nomor_kartu || data.NAMAPASIEN) {
        html += `
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong>Nomor Kartu:</strong> ${data.nomor_kartu || '-'}
                </div>
                <div class="col-md-6">
                    <strong>Nama Pasien:</strong> ${data.NAMAPASIEN || '-'}
                </div>
            </div>
        `;
    }
    
    if (data.id_perjanjian) {
        html += `
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong>ID Perjanjian:</strong> ${data.id_perjanjian}
                </div>
                ${data.task_id ? `<div class="col-md-6"><strong>Task ID:</strong> ${data.task_id}</div>` : ''}
            </div>
        `;
    }
    
    if (data.suratkontrol) {
        html += `
            <div class="row mb-3">
                <div class="col-12">
                    <strong>Nomor Surat Kontrol:</strong> ${data.suratkontrol}
                </div>
            </div>
        `;
    }
    
    // Request and Response
    html += `
        <div class="row">
            <div class="col-md-6">
                <h6>Request:</h6>
                <pre class="bg-light p-2 rounded" style="max-height: 200px; overflow-y: auto;">${JSON.stringify(data.request_formatted, null, 2)}</pre>
            </div>
            <div class="col-md-6">
                <h6>Response:</h6>
                <pre class="bg-light p-2 rounded" style="max-height: 200px; overflow-y: auto;">${JSON.stringify(data.response_formatted, null, 2)}</pre>
            </div>
        </div>
    `;
    
    return html;
}

function bindEvents() {
    // Sidebar navigation
    $('.nav-link').click(function(e) {
        e.preventDefault();
        $('.nav-link').removeClass('active');
        $(this).addClass('active');

        currentLogType = $(this).data('log-type');
        currentPage = 1;

        updateTableTitle();
        loadLogs();
    });

    // Filter buttons - use both ID and onclick for compatibility
    $(document).on('click', '#applyFilter', function(e) {
        e.preventDefault();
        window.applyFilter();
    });

    $(document).on('click', '#resetFilter', function(e) {
        e.preventDefault();
        window.resetFilter();
    });

    // Search on enter
    $(document).on('keypress', '#searchText', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            window.applyFilter();
        }
    });

    // Also bind to form elements for change events
    $(document).on('change', '#dateFrom, #dateTo, #appointmentDateFrom, #appointmentDateTo, #statusFilter', function() {
        // Auto-apply filter when date or status changes (optional)
        // Uncomment the line below if you want auto-filter
        // window.applyFilter();
    });
}

function initDataTable() {
    // Don't initialize DataTable, just use regular table
    // DataTable causes issues with dynamic content
    if (dataTable) {
        try {
            dataTable.destroy();
        } catch (e) {
            console.log('DataTable destroy error:', e);
        }
        dataTable = null;
    }
}

// Global init function
function init() {
    // Set default dates
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(formatDate(today));
    $('#dateFrom').val(formatDate(lastWeek));

    // Load initial data
    loadStats();
    loadLogs();

    // Initialize DataTable
    initDataTable();

    // Bind events
    bindEvents();
}

// Global functions for HTML onclick events
window.applyFilter = function() {
    console.log('applyFilter() called');
    console.log('Filter values:', {
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        appointmentDateFrom: $('#appointmentDateFrom').val(),
        appointmentDateTo: $('#appointmentDateTo').val(),
        status: $('#statusFilter').val(),
        search: $('#searchText').val()
    });
    currentPage = 1;
    loadLogs();
};

window.resetFilter = function() {
    $('#dateFrom').val('');
    $('#dateTo').val('');
    $('#appointmentDateFrom').val('');
    $('#appointmentDateTo').val('');
    $('#statusFilter').val('');
    $('#searchText').val('');
    window.applyFilter();
};

window.refreshData = function() {
    loadStats();
    loadLogs();
};

window.changePage = function(page) {
    if (page >= 1) {
        currentPage = page;
        loadLogs();
    }
};

window.toggleSidebar = function() {
    $('.sidebar').toggleClass('show');
};

window.showDetail = showDetail;

// Initialize when document is ready
$(document).ready(function() {
    init();
    
    // Close sidebar when clicking outside on mobile
    $(document).click(function(e) {
        if ($(window).width() <= 768) {
            if (!$(e.target).closest('.sidebar, .btn[onclick="toggleSidebar()"]').length) {
                $('.sidebar').removeClass('show');
            }
        }
    });
});

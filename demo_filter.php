<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Filter - Dashboard Log BPJS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background: #f8f9fa; }
        .demo-section { margin: 20px 0; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .filter-section { background-color: #f8f9fa; border-radius: 10px; padding: 20px; margin-bottom: 20px; }
        .log-output { background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; max-height: 300px; overflow-y: auto; }
        .status-badge { font-size: 0.8em; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-filter"></i> Demo Filter Dashboard</h1>
        
        <!-- Filter Section -->
        <div class="demo-section">
            <h3><i class="fas fa-sliders-h"></i> Filter Controls</h3>
            <div class="filter-section">
                <h5><i class="fas fa-filter"></i> Filter Data</h5>
                <div class="row">
                    <div class="col-md-3">
                        <label for="dateFrom" class="form-label">Dari Tanggal</label>
                        <input type="date" class="form-control" id="dateFrom">
                    </div>
                    <div class="col-md-3">
                        <label for="dateTo" class="form-label">Sampai Tanggal</label>
                        <input type="date" class="form-control" id="dateTo">
                    </div>
                    <div class="col-md-3">
                        <label for="statusFilter" class="form-label">Status</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">Semua Status</option>
                            <option value="200">Sukses (200)</option>
                            <option value="201">Created (201)</option>
                            <option value="203">Error 203</option>
                            <option value="204">Error 204</option>
                            <option value="400">Bad Request (400)</option>
                            <option value="500">Server Error (500)</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="searchText" class="form-label">Pencarian</label>
                        <input type="text" class="form-control" id="searchText" placeholder="Cari nomor kartu, nama...">
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" id="applyFilter" onclick="applyFilter()">
                            <i class="fas fa-search"></i> Terapkan Filter
                        </button>
                        <button type="button" class="btn btn-secondary" id="resetFilter" onclick="resetFilter()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                        <button type="button" class="btn btn-info" onclick="showCurrentValues()">
                            <i class="fas fa-eye"></i> Lihat Nilai
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demo Data Table -->
        <div class="demo-section">
            <h3><i class="fas fa-table"></i> Demo Data (Filtered)</h3>
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="logTable">
                    <thead class="table-dark">
                        <tr>
                            <th>No</th>
                            <th>Tanggal</th>
                            <th>Jenis Log</th>
                            <th>Status</th>
                            <th>Nomor Kartu</th>
                            <th>Nama Pasien</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody id="logTableBody">
                        <!-- Data akan dimuat di sini -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Debug Output -->
        <div class="demo-section">
            <h3><i class="fas fa-bug"></i> Debug Output</h3>
            <div class="log-output" id="debugOutput">
                <div>Debug output akan muncul di sini...</div>
            </div>
            <button class="btn btn-warning mt-2" onclick="clearDebug()">
                <i class="fas fa-trash"></i> Clear Debug
            </button>
        </div>

        <!-- Navigation -->
        <div class="demo-section">
            <h3><i class="fas fa-compass"></i> Navigation</h3>
            <div class="row">
                <div class="col-md-3">
                    <a href="dashboard.php" class="btn btn-success w-100">
                        <i class="fas fa-tachometer-alt"></i> Dashboard Utama
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="test_filter.php" class="btn btn-info w-100">
                        <i class="fas fa-vial"></i> Test Filter
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="demo.php" class="btn btn-warning w-100">
                        <i class="fas fa-play"></i> Demo Utama
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="index.php" class="btn btn-secondary w-100">
                        <i class="fas fa-home"></i> Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Demo data
        const demoData = [
            {
                id: 'demo1',
                tanggal: '2024-01-15 10:30:25',
                jenis_log: 'Surat Kontrol',
                status: '200',
                nomor_kartu: '0001234567890',
                nama_pasien: 'Ahmad Suryadi'
            },
            {
                id: 'demo2',
                tanggal: '2024-01-15 10:25:12',
                jenis_log: 'Tambah Antrean',
                status: '203',
                nomor_kartu: '0009876543210',
                nama_pasien: 'Siti Nurhaliza'
            },
            {
                id: 'demo3',
                tanggal: '2024-01-14 15:20:45',
                jenis_log: 'Batal Surat Kontrol',
                status: '200',
                nomor_kartu: '0005555666777',
                nama_pasien: 'Budi Santoso'
            },
            {
                id: 'demo4',
                tanggal: '2024-01-14 14:15:30',
                jenis_log: 'Update Antrean',
                status: '400',
                nomor_kartu: '0001111222333',
                nama_pasien: 'Dewi Sartika'
            },
            {
                id: 'demo5',
                tanggal: '2024-01-13 09:45:15',
                jenis_log: 'Batal Antrean',
                status: '201',
                nomor_kartu: '0007777888999',
                nama_pasien: 'Rudi Hartono'
            }
        ];

        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const debugDiv = document.getElementById('debugOutput');
            debugDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }

        function getStatusBadge(status) {
            let badgeClass = 'bg-secondary';
            let statusText = status;
            
            switch (status) {
                case '200':
                case '201':
                    badgeClass = 'bg-success';
                    statusText = 'Sukses (' + status + ')';
                    break;
                case '203':
                case '204':
                case '400':
                case '404':
                    badgeClass = 'bg-warning';
                    statusText = 'Warning (' + status + ')';
                    break;
                case '500':
                    badgeClass = 'bg-danger';
                    statusText = 'Error (' + status + ')';
                    break;
            }
            
            return `<span class="badge status-badge ${badgeClass}">${statusText}</span>`;
        }

        function filterData() {
            const dateFrom = $('#dateFrom').val();
            const dateTo = $('#dateTo').val();
            const status = $('#statusFilter').val();
            const search = $('#searchText').val().toLowerCase();

            debugLog(`Filtering with: dateFrom=${dateFrom}, dateTo=${dateTo}, status=${status}, search=${search}`);

            let filteredData = demoData.filter(item => {
                // Date filter
                if (dateFrom) {
                    const itemDate = item.tanggal.split(' ')[0];
                    if (itemDate < dateFrom) return false;
                }
                if (dateTo) {
                    const itemDate = item.tanggal.split(' ')[0];
                    if (itemDate > dateTo) return false;
                }

                // Status filter
                if (status && item.status !== status) return false;

                // Search filter
                if (search) {
                    const searchableText = (item.nomor_kartu + ' ' + item.nama_pasien + ' ' + item.jenis_log).toLowerCase();
                    if (searchableText.indexOf(search) === -1) return false;
                }

                return true;
            });

            debugLog(`Filtered ${filteredData.length} items from ${demoData.length} total`);
            return filteredData;
        }

        function renderTable(data) {
            let html = '';
            
            if (data.length === 0) {
                html = '<tr><td colspan="7" class="text-center">Tidak ada data yang sesuai filter</td></tr>';
            } else {
                data.forEach((item, index) => {
                    html += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${item.tanggal}</td>
                            <td><span class="badge bg-info">${item.jenis_log}</span></td>
                            <td>${getStatusBadge(item.status)}</td>
                            <td>${item.nomor_kartu}</td>
                            <td>${item.nama_pasien}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="showDetail('${item.id}')">
                                    <i class="fas fa-eye"></i> Detail
                                </button>
                            </td>
                        </tr>
                    `;
                });
            }
            
            $('#logTableBody').html(html);
        }

        // Global functions
        window.applyFilter = function() {
            debugLog('applyFilter() called');
            const filteredData = filterData();
            renderTable(filteredData);
        };

        window.resetFilter = function() {
            debugLog('resetFilter() called');
            $('#dateFrom').val('');
            $('#dateTo').val('');
            $('#statusFilter').val('');
            $('#searchText').val('');
            
            // Show all data after reset
            renderTable(demoData);
            debugLog('Filter reset completed');
        };

        function showCurrentValues() {
            const values = {
                dateFrom: $('#dateFrom').val(),
                dateTo: $('#dateTo').val(),
                status: $('#statusFilter').val(),
                search: $('#searchText').val()
            };
            debugLog('Current filter values: ' + JSON.stringify(values, null, 2));
        }

        function showDetail(id) {
            const item = demoData.find(d => d.id === id);
            if (item) {
                alert('Detail untuk: ' + item.nama_pasien + '\nNomor Kartu: ' + item.nomor_kartu);
            }
        }

        function clearDebug() {
            $('#debugOutput').html('<div>Debug output cleared...</div>');
        }

        // Initialize
        $(document).ready(function() {
            debugLog('Demo Filter initialized');
            
            // Set default dates
            const today = new Date();
            const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            
            $('#dateTo').val(today.toISOString().split('T')[0]);
            $('#dateFrom').val(lastWeek.toISOString().split('T')[0]);
            
            // Show all data initially
            renderTable(demoData);
            
            // Bind events
            $('#searchText').keypress(function(e) {
                if (e.which === 13) {
                    debugLog('Enter key pressed in search');
                    applyFilter();
                }
            });
            
            debugLog('Demo ready - try using the filter controls!');
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Filter <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .test-container { background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .test-section { margin: 25px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; }
        .result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .result.success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .result.error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .result.info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .api-response { background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; max-height: 400px; overflow-y: auto; }
        .filter-demo { background: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="text-center mb-4">
                <h1 class="text-primary"><i class="fas fa-calendar-check"></i> Test Filter Tanggal Perjanjian</h1>
                <p class="text-muted">Test filter berdasarkan tanggal perjanjian dari data JSON request</p>
            </div>

            <!-- Filter Demo -->
            <div class="test-section">
                <h3><i class="fas fa-filter"></i> Filter Demo</h3>
                <div class="filter-demo">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="dateFrom" class="form-label">Dari Tanggal Log</label>
                            <input type="date" class="form-control" id="dateFrom">
                        </div>
                        <div class="col-md-3">
                            <label for="dateTo" class="form-label">Sampai Tanggal Log</label>
                            <input type="date" class="form-control" id="dateTo">
                        </div>
                        <div class="col-md-3">
                            <label for="appointmentDateFrom" class="form-label">Dari Tanggal Perjanjian</label>
                            <input type="date" class="form-control" id="appointmentDateFrom">
                        </div>
                        <div class="col-md-3">
                            <label for="appointmentDateTo" class="form-label">Sampai Tanggal Perjanjian</label>
                            <input type="date" class="form-control" id="appointmentDateTo">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <label for="statusFilter" class="form-label">Status</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">Semua Status</option>
                                <option value="200">Sukses (200)</option>
                                <option value="201">Created (201)</option>
                                <option value="203">Error 203</option>
                                <option value="400">Bad Request (400)</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="searchText" class="form-label">Pencarian</label>
                            <input type="text" class="form-control" id="searchText" placeholder="Cari...">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="testFilter()">
                                    <i class="fas fa-search"></i> Test Filter
                                </button>
                                <button class="btn btn-secondary" onclick="resetForm()">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Tests -->
            <div class="test-section">
                <h3><i class="fas fa-bolt"></i> Quick Tests</h3>
                <div class="row">
                    <div class="col-md-4">
                        <button class="btn btn-success w-100 mb-2" onclick="testAppointmentDateOnly()">
                            <i class="fas fa-calendar"></i> Test Tanggal Perjanjian Saja
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-info w-100 mb-2" onclick="testLogDateOnly()">
                            <i class="fas fa-clock"></i> Test Tanggal Log Saja
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-warning w-100 mb-2" onclick="testBothDates()">
                            <i class="fas fa-calendar-alt"></i> Test Kedua Tanggal
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results -->
            <div class="test-section">
                <h3><i class="fas fa-chart-bar"></i> Test Results</h3>
                <div id="testResults">
                    <div class="result info">
                        <i class="fas fa-info-circle"></i> Klik tombol test untuk memulai...
                    </div>
                </div>
            </div>

            <!-- API Response -->
            <div class="test-section">
                <h3><i class="fas fa-code"></i> API Response</h3>
                <div class="api-response" id="apiResponse">
                    Response API akan muncul di sini...
                </div>
            </div>

            <!-- Navigation -->
            <div class="test-section">
                <h3><i class="fas fa-compass"></i> Navigation</h3>
                <div class="row">
                    <div class="col-md-3">
                        <a href="dashboard.php" class="btn btn-success w-100">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="test_database_filter.php" class="btn btn-info w-100">
                            <i class="fas fa-database"></i> Test Database
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="demo_filter.php" class="btn btn-warning w-100">
                            <i class="fas fa-play"></i> Demo Filter
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="index.php" class="btn btn-secondary w-100">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function addResult(type, message) {
            const icon = type === 'success' ? 'fa-check' : 
                        type === 'error' ? 'fa-times' : 'fa-info-circle';
            
            const html = `
                <div class="result ${type}">
                    <i class="fas ${icon}"></i> ${message}
                </div>
            `;
            $('#testResults').append(html);
        }

        function showApiResponse(response) {
            $('#apiResponse').html(JSON.stringify(response, null, 2));
        }

        function testAPI(params, testName) {
            addResult('info', `Testing: ${testName}`);
            addResult('info', `Parameters: ${JSON.stringify(params)}`);
            
            $.ajax({
                url: 'api/logs_simple.php',
                method: 'GET',
                data: params,
                dataType: 'json',
                success: function(response) {
                    showApiResponse(response);
                    
                    if (response.success) {
                        const data = response.data;
                        addResult('success', `${testName} - Success! Total: ${data.total}, Displayed: ${data.data ? data.data.length : 0}`);
                        
                        if (data.data && data.data.length > 0) {
                            // Show sample data with appointment date if available
                            const sample = data.data[0];
                            let sampleInfo = `Sample: ${sample.jenis_log} - ${sample.status} - ${sample.tanggal}`;
                            
                            // Try to extract appointment date from detail
                            if (sample.detail && sample.detail.request) {
                                const request = typeof sample.detail.request === 'string' ? 
                                    JSON.parse(sample.detail.request) : sample.detail.request;
                                if (request.tglRencanaKontrol) {
                                    sampleInfo += ` - Perjanjian: ${request.tglRencanaKontrol}`;
                                }
                            }
                            
                            addResult('info', sampleInfo);
                        }
                    } else {
                        addResult('error', `${testName} - Error: ${response.message}`);
                    }
                },
                error: function(xhr, status, error) {
                    addResult('error', `${testName} - AJAX Error: ${error}`);
                    showApiResponse({error: error, status: status});
                }
            });
        }

        function testFilter() {
            const params = {
                type: 'all',
                page: 1,
                limit: 10,
                date_from: $('#dateFrom').val(),
                date_to: $('#dateTo').val(),
                appointment_date_from: $('#appointmentDateFrom').val(),
                appointment_date_to: $('#appointmentDateTo').val(),
                status: $('#statusFilter').val(),
                search: $('#searchText').val()
            };
            
            // Remove empty parameters
            Object.keys(params).forEach(key => {
                if (params[key] === '') {
                    delete params[key];
                }
            });
            
            testAPI(params, 'Custom Filter');
        }

        function testAppointmentDateOnly() {
            const params = {
                type: 'all',
                page: 1,
                limit: 10,
                appointment_date_from: '2024-01-01',
                appointment_date_to: '2024-12-31'
            };
            testAPI(params, 'Appointment Date Filter Only');
        }

        function testLogDateOnly() {
            const params = {
                type: 'all',
                page: 1,
                limit: 10,
                date_from: '2024-01-01',
                date_to: '2024-12-31'
            };
            testAPI(params, 'Log Date Filter Only');
        }

        function testBothDates() {
            const params = {
                type: 'all',
                page: 1,
                limit: 10,
                date_from: '2024-01-01',
                date_to: '2024-12-31',
                appointment_date_from: '2024-01-01',
                appointment_date_to: '2024-12-31'
            };
            testAPI(params, 'Both Date Filters');
        }

        function resetForm() {
            $('#dateFrom').val('');
            $('#dateTo').val('');
            $('#appointmentDateFrom').val('');
            $('#appointmentDateTo').val('');
            $('#statusFilter').val('');
            $('#searchText').val('');
            addResult('info', 'Form reset');
        }

        // Initialize
        $(document).ready(function() {
            addResult('info', 'Appointment Date Filter Test initialized');
            addResult('info', 'This test filters based on tglRencanaKontrol in JSON request data');
            
            // Set default dates
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            
            $('#dateFrom').val(firstDay.toISOString().split('T')[0]);
            $('#dateTo').val(today.toISOString().split('T')[0]);
            $('#appointmentDateFrom').val(firstDay.toISOString().split('T')[0]);
            $('#appointmentDateTo').val(today.toISOString().split('T')[0]);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Filter Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background: #f8f9fa; }
        .test-section { margin: 20px 0; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-result.success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .test-result.error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .test-result.info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .filter-demo { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-filter"></i> Test Filter Dashboard</h1>
        
        <!-- Filter Demo Section -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> Filter Demo</h3>
            <p>Test filter functionality dengan form yang sama seperti di dashboard:</p>
            
            <div class="filter-demo">
                <div class="row">
                    <div class="col-md-3">
                        <label for="dateFrom" class="form-label">Dari Tanggal</label>
                        <input type="date" class="form-control" id="dateFrom">
                    </div>
                    <div class="col-md-3">
                        <label for="dateTo" class="form-label">Sampai Tanggal</label>
                        <input type="date" class="form-control" id="dateTo">
                    </div>
                    <div class="col-md-3">
                        <label for="statusFilter" class="form-label">Status</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">Semua Status</option>
                            <option value="200">Sukses (200)</option>
                            <option value="201">Created (201)</option>
                            <option value="203">Error 203</option>
                            <option value="204">Error 204</option>
                            <option value="400">Bad Request (400)</option>
                            <option value="500">Server Error (500)</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="searchText" class="form-label">Pencarian</label>
                        <input type="text" class="form-control" id="searchText" placeholder="Cari nomor kartu, nama...">
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" id="applyFilter" onclick="applyFilter()">
                            <i class="fas fa-search"></i> Terapkan Filter
                        </button>
                        <button type="button" class="btn btn-secondary" id="resetFilter" onclick="resetFilter()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                        <button type="button" class="btn btn-info" onclick="testFilterValues()">
                            <i class="fas fa-eye"></i> Lihat Nilai Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h3><i class="fas fa-check-circle"></i> Test Results</h3>
            <div id="testResults">
                <div class="test-result info">
                    <i class="fas fa-info-circle"></i> Klik tombol test untuk memulai...
                </div>
            </div>
        </div>

        <!-- Manual Tests -->
        <div class="test-section">
            <h3><i class="fas fa-hand-pointer"></i> Manual Tests</h3>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-primary w-100 mb-2" onclick="testApplyFilter()">
                        <i class="fas fa-play"></i> Test Apply Filter
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-secondary w-100 mb-2" onclick="testResetFilter()">
                        <i class="fas fa-undo"></i> Test Reset Filter
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-info w-100 mb-2" onclick="testSearchEnter()">
                        <i class="fas fa-keyboard"></i> Test Search Enter
                    </button>
                </div>
            </div>
        </div>

        <!-- API Test -->
        <div class="test-section">
            <h3><i class="fas fa-server"></i> API Filter Test</h3>
            <button class="btn btn-success" onclick="testAPIWithFilter()">
                <i class="fas fa-play"></i> Test API dengan Filter
            </button>
            <div id="apiResults" class="mt-3"></div>
        </div>

        <!-- Navigation -->
        <div class="test-section">
            <h3><i class="fas fa-compass"></i> Navigation</h3>
            <div class="row">
                <div class="col-md-4">
                    <a href="dashboard.php" class="btn btn-success w-100">
                        <i class="fas fa-tachometer-alt"></i> Dashboard Utama
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="test_fixed.php" class="btn btn-info w-100">
                        <i class="fas fa-vial"></i> Test Fixed
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="demo.php" class="btn btn-warning w-100">
                        <i class="fas fa-play"></i> Demo
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/dashboard_fixed.js"></script>
    
    <script>
        function addTestResult(type, message) {
            const icon = type === 'success' ? 'fa-check' : 
                        type === 'error' ? 'fa-times' : 'fa-info-circle';
            
            const html = `
                <div class="test-result ${type}">
                    <i class="fas ${icon}"></i> ${message}
                </div>
            `;
            $('#testResults').append(html);
        }

        function testFilterValues() {
            const values = {
                dateFrom: $('#dateFrom').val(),
                dateTo: $('#dateTo').val(),
                status: $('#statusFilter').val(),
                search: $('#searchText').val()
            };
            
            addTestResult('info', 'Filter Values: ' + JSON.stringify(values, null, 2));
        }

        function testApplyFilter() {
            try {
                // Set some test values
                $('#dateFrom').val('2024-01-01');
                $('#dateTo').val('2024-01-31');
                $('#statusFilter').val('200');
                $('#searchText').val('test');
                
                // Test the function
                if (typeof window.applyFilter === 'function') {
                    window.applyFilter();
                    addTestResult('success', 'applyFilter() executed successfully');
                    testFilterValues();
                } else {
                    addTestResult('error', 'applyFilter() function not found');
                }
            } catch (e) {
                addTestResult('error', 'applyFilter() error: ' + e.message);
            }
        }

        function testResetFilter() {
            try {
                // Set some values first
                $('#dateFrom').val('2024-01-01');
                $('#dateTo').val('2024-01-31');
                $('#statusFilter').val('200');
                $('#searchText').val('test');
                
                addTestResult('info', 'Values before reset: ' + JSON.stringify({
                    dateFrom: $('#dateFrom').val(),
                    dateTo: $('#dateTo').val(),
                    status: $('#statusFilter').val(),
                    search: $('#searchText').val()
                }));
                
                // Test reset
                if (typeof window.resetFilter === 'function') {
                    window.resetFilter();
                    addTestResult('success', 'resetFilter() executed successfully');
                    
                    // Check if values are cleared
                    setTimeout(() => {
                        const afterReset = {
                            dateFrom: $('#dateFrom').val(),
                            dateTo: $('#dateTo').val(),
                            status: $('#statusFilter').val(),
                            search: $('#searchText').val()
                        };
                        
                        if (!afterReset.dateFrom && !afterReset.dateTo && !afterReset.status && !afterReset.search) {
                            addTestResult('success', 'All filter values cleared successfully');
                        } else {
                            addTestResult('error', 'Some values not cleared: ' + JSON.stringify(afterReset));
                        }
                    }, 100);
                } else {
                    addTestResult('error', 'resetFilter() function not found');
                }
            } catch (e) {
                addTestResult('error', 'resetFilter() error: ' + e.message);
            }
        }

        function testSearchEnter() {
            try {
                $('#searchText').val('test search');
                
                // Simulate Enter key press
                const event = new KeyboardEvent('keypress', {
                    key: 'Enter',
                    which: 13,
                    keyCode: 13
                });
                
                document.getElementById('searchText').dispatchEvent(event);
                addTestResult('success', 'Enter key simulation completed');
            } catch (e) {
                addTestResult('error', 'Search Enter test error: ' + e.message);
            }
        }

        function testAPIWithFilter() {
            $('#apiResults').html('<i class="fas fa-spinner fa-spin"></i> Testing API with filter...');
            
            const params = {
                type: 'all',
                page: 1,
                limit: 5,
                date_from: '2024-01-01',
                date_to: '2024-01-31',
                status: '200',
                search: 'test'
            };
            
            $.ajax({
                url: 'api/logs_simple.php',
                method: 'GET',
                data: params,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#apiResults').html(`
                            <div class="test-result success">
                                <i class="fas fa-check"></i> API Filter Test Success<br>
                                <small>Total: ${response.data.total}, Page: ${response.data.page}</small><br>
                                <small>Parameters sent: ${JSON.stringify(params)}</small>
                            </div>
                        `);
                    } else {
                        $('#apiResults').html(`
                            <div class="test-result error">
                                <i class="fas fa-times"></i> API Error: ${response.message}
                            </div>
                        `);
                    }
                },
                error: function() {
                    $('#apiResults').html(`
                        <div class="test-result error">
                            <i class="fas fa-times"></i> API Connection Error
                        </div>
                    `);
                }
            });
        }

        // Auto test on page load
        $(document).ready(function() {
            // Set default dates
            const today = new Date();
            const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            
            $('#dateTo').val(today.toISOString().split('T')[0]);
            $('#dateFrom').val(lastWeek.toISOString().split('T')[0]);
            
            // Test if functions exist
            setTimeout(() => {
                if (typeof window.applyFilter === 'function') {
                    addTestResult('success', 'applyFilter() function exists');
                } else {
                    addTestResult('error', 'applyFilter() function missing');
                }
                
                if (typeof window.resetFilter === 'function') {
                    addTestResult('success', 'resetFilter() function exists');
                } else {
                    addTestResult('error', 'resetFilter() function missing');
                }
                
                // Test button click events
                if ($('#applyFilter').length) {
                    addTestResult('success', 'Apply Filter button found');
                } else {
                    addTestResult('error', 'Apply Filter button missing');
                }
                
                if ($('#resetFilter').length) {
                    addTestResult('success', 'Reset Filter button found');
                } else {
                    addTestResult('error', 'Reset Filter button missing');
                }
            }, 500);
        });
    </script>
</body>
</html>

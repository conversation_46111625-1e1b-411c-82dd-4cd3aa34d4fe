<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Simple Dashboard</h1>
        
        <div class="test-section">
            <h3>1. Test API Stats</h3>
            <button class="btn btn-primary" onclick="testStats()">Test Stats API</button>
            <div id="statsResult" class="mt-2"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Test API Logs</h3>
            <button class="btn btn-primary" onclick="testLogs()">Test Logs API</button>
            <div id="logsResult" class="mt-2"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Test JavaScript Functions</h3>
            <button class="btn btn-secondary" onclick="testJSFunctions()">Test JS Functions</button>
            <div id="jsResult" class="mt-2"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Test Table Rendering</h3>
            <button class="btn btn-info" onclick="testTableRender()">Test Table</button>
            <div id="tableResult" class="mt-2">
                <table class="table table-striped" id="testTable">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Tanggal</th>
                            <th>Jenis Log</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody id="testTableBody">
                        <!-- Data akan dimuat di sini -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="test-section">
            <h3>5. Navigation Test</h3>
            <div class="row">
                <div class="col-md-4">
                    <a href="dashboard.php" class="btn btn-success w-100">
                        <i class="fas fa-tachometer-alt"></i> Dashboard Utama
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="demo.php" class="btn btn-warning w-100">
                        <i class="fas fa-play"></i> Demo
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="test_api.php" class="btn btn-info w-100">
                        <i class="fas fa-cog"></i> Test API
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testStats() {
            $('#statsResult').html('<i class="fas fa-spinner fa-spin"></i> Testing...');
            
            $.ajax({
                url: 'api/logs_simple.php?action=stats',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#statsResult').html(`
                            <div class="success">
                                <i class="fas fa-check"></i> Stats API berhasil!<br>
                                Total: ${response.data.total}<br>
                                Success: ${response.data.success}<br>
                                Error: ${response.data.error}<br>
                                Today: ${response.data.today}
                            </div>
                        `);
                    } else {
                        $('#statsResult').html(`<div class="error"><i class="fas fa-times"></i> Error: ${response.message}</div>`);
                    }
                },
                error: function(xhr, status, error) {
                    $('#statsResult').html(`<div class="error"><i class="fas fa-times"></i> AJAX Error: ${error}</div>`);
                }
            });
        }
        
        function testLogs() {
            $('#logsResult').html('<i class="fas fa-spinner fa-spin"></i> Testing...');
            
            $.ajax({
                url: 'api/logs_simple.php?type=all&limit=5',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const logs = response.data.data || [];
                        $('#logsResult').html(`
                            <div class="success">
                                <i class="fas fa-check"></i> Logs API berhasil!<br>
                                Total data: ${response.data.total}<br>
                                Data yang ditampilkan: ${logs.length}<br>
                                Page: ${response.data.page}<br>
                                Total pages: ${response.data.total_pages}
                            </div>
                        `);
                    } else {
                        $('#logsResult').html(`<div class="error"><i class="fas fa-times"></i> Error: ${response.message}</div>`);
                    }
                },
                error: function(xhr, status, error) {
                    $('#logsResult').html(`<div class="error"><i class="fas fa-times"></i> AJAX Error: ${error}</div>`);
                }
            });
        }
        
        function testJSFunctions() {
            let results = [];
            
            // Test global functions
            if (typeof window.applyFilter === 'function') {
                results.push('<span class="success">✓ applyFilter function exists</span>');
            } else {
                results.push('<span class="error">✗ applyFilter function missing</span>');
            }
            
            if (typeof window.resetFilter === 'function') {
                results.push('<span class="success">✓ resetFilter function exists</span>');
            } else {
                results.push('<span class="error">✗ resetFilter function missing</span>');
            }
            
            if (typeof window.refreshData === 'function') {
                results.push('<span class="success">✓ refreshData function exists</span>');
            } else {
                results.push('<span class="error">✗ refreshData function missing</span>');
            }
            
            if (typeof window.toggleSidebar === 'function') {
                results.push('<span class="success">✓ toggleSidebar function exists</span>');
            } else {
                results.push('<span class="error">✗ toggleSidebar function missing</span>');
            }
            
            $('#jsResult').html(results.join('<br>'));
        }
        
        function testTableRender() {
            // Test data
            const testData = [
                {
                    id: 'test1',
                    tanggal: '2024-01-15 10:30:25',
                    jenis_log: 'Test Log',
                    status: '200',
                    nomor_kartu: '**********',
                    nama_pasien: 'Test Patient'
                },
                {
                    id: 'test2',
                    tanggal: '2024-01-15 10:25:12',
                    jenis_log: 'Another Test',
                    status: '203',
                    nomor_kartu: '**********',
                    nama_pasien: 'Another Patient'
                }
            ];
            
            let html = '';
            testData.forEach(function(log, index) {
                const statusBadge = getStatusBadge(log.status);
                
                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${log.tanggal}</td>
                        <td><span class="badge bg-info">${log.jenis_log}</span></td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="alert('Detail for ${log.id}')">
                                <i class="fas fa-eye"></i> Detail
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            $('#testTableBody').html(html);
            $('#tableResult').prepend('<div class="success"><i class="fas fa-check"></i> Table rendered successfully!</div>');
        }
        
        function getStatusBadge(status) {
            let badgeClass = 'bg-secondary';
            let statusText = status;
            
            switch (status) {
                case '200':
                case '201':
                    badgeClass = 'bg-success';
                    statusText = 'Sukses (' + status + ')';
                    break;
                case '203':
                case '204':
                case '400':
                case '404':
                    badgeClass = 'bg-warning';
                    statusText = 'Warning (' + status + ')';
                    break;
                case '500':
                    badgeClass = 'bg-danger';
                    statusText = 'Error (' + status + ')';
                    break;
            }
            
            return `<span class="badge ${badgeClass}">${statusText}</span>`;
        }
        
        // Auto run tests on page load
        $(document).ready(function() {
            setTimeout(function() {
                testJSFunctions();
            }, 500);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Test - Dashboard Log BPJS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background: #f8f9fa; }
        .test-card { margin: 15px 0; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-result { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
            border-left: 4px solid #ddd;
        }
        .test-result.success { border-left-color: #28a745; background: #d4edda; }
        .test-result.error { border-left-color: #dc3545; background: #f8d7da; }
        .test-result.warning { border-left-color: #ffc107; background: #fff3cd; }
        .test-result.info { border-left-color: #17a2b8; background: #d1ecf1; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-check-circle"></i> Final Test - Dashboard Log BPJS</h3>
                        <p class="mb-0">Verifikasi semua komponen dashboard berfungsi dengan baik</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Test 1: JavaScript Functions -->
                        <div class="card test-card">
                            <div class="card-header">
                                <h5><i class="fas fa-code"></i> Test 1: JavaScript Functions</h5>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-primary" onclick="testJavaScriptFunctions()">
                                    <i class="fas fa-play"></i> Test JS Functions
                                </button>
                                <div id="jsTestResult" class="mt-3"></div>
                            </div>
                        </div>

                        <!-- Test 2: API Endpoints -->
                        <div class="card test-card">
                            <div class="card-header">
                                <h5><i class="fas fa-server"></i> Test 2: API Endpoints</h5>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-success" onclick="testAPIEndpoints()">
                                    <i class="fas fa-play"></i> Test API
                                </button>
                                <div id="apiTestResult" class="mt-3"></div>
                            </div>
                        </div>

                        <!-- Test 3: Dashboard Components -->
                        <div class="card test-card">
                            <div class="card-header">
                                <h5><i class="fas fa-tachometer-alt"></i> Test 3: Dashboard Components</h5>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-info" onclick="testDashboardComponents()">
                                    <i class="fas fa-play"></i> Test Components
                                </button>
                                <div id="componentTestResult" class="mt-3"></div>
                            </div>
                        </div>

                        <!-- Test 4: Error Handling -->
                        <div class="card test-card">
                            <div class="card-header">
                                <h5><i class="fas fa-shield-alt"></i> Test 4: Error Handling</h5>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-warning" onclick="testErrorHandling()">
                                    <i class="fas fa-play"></i> Test Error Handling
                                </button>
                                <div id="errorTestResult" class="mt-3"></div>
                            </div>
                        </div>

                        <!-- Navigation Links -->
                        <div class="card test-card">
                            <div class="card-header">
                                <h5><i class="fas fa-link"></i> Navigation</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <a href="dashboard.php" class="btn btn-success w-100 mb-2">
                                            <i class="fas fa-tachometer-alt"></i><br>Dashboard Utama
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="demo.php" class="btn btn-warning w-100 mb-2">
                                            <i class="fas fa-play"></i><br>Demo
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="test_simple.php" class="btn btn-info w-100 mb-2">
                                            <i class="fas fa-vial"></i><br>Test Simple
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="index.php" class="btn btn-secondary w-100 mb-2">
                                            <i class="fas fa-home"></i><br>Home
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Run All Tests -->
                        <div class="text-center mt-4">
                            <button class="btn btn-primary btn-lg" onclick="runAllTests()">
                                <i class="fas fa-rocket"></i> Run All Tests
                            </button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testJavaScriptFunctions() {
            $('#jsTestResult').html('<i class="fas fa-spinner fa-spin"></i> Testing JavaScript functions...');
            
            let results = [];
            let allPassed = true;
            
            // Test global functions existence
            const functionsToTest = [
                'applyFilter',
                'resetFilter', 
                'refreshData',
                'changePage',
                'toggleSidebar'
            ];
            
            functionsToTest.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    results.push(`<div class="test-result success"><i class="fas fa-check"></i> ${funcName}() - OK</div>`);
                } else {
                    results.push(`<div class="test-result error"><i class="fas fa-times"></i> ${funcName}() - MISSING</div>`);
                    allPassed = false;
                }
            });
            
            // Test jQuery
            if (typeof $ !== 'undefined') {
                results.push(`<div class="test-result success"><i class="fas fa-check"></i> jQuery - OK</div>`);
            } else {
                results.push(`<div class="test-result error"><i class="fas fa-times"></i> jQuery - MISSING</div>`);
                allPassed = false;
            }
            
            const summary = allPassed ? 
                '<div class="test-result success"><strong><i class="fas fa-check-circle"></i> All JavaScript functions are working!</strong></div>' :
                '<div class="test-result error"><strong><i class="fas fa-exclamation-triangle"></i> Some JavaScript functions have issues!</strong></div>';
            
            $('#jsTestResult').html(summary + results.join(''));
        }
        
        function testAPIEndpoints() {
            $('#apiTestResult').html('<i class="fas fa-spinner fa-spin"></i> Testing API endpoints...');
            
            let results = [];
            let testsCompleted = 0;
            let totalTests = 2;
            
            function checkCompletion() {
                testsCompleted++;
                if (testsCompleted === totalTests) {
                    $('#apiTestResult').html(results.join(''));
                }
            }
            
            // Test Stats API
            $.ajax({
                url: 'api/logs_simple.php?action=stats',
                method: 'GET',
                dataType: 'json',
                timeout: 5000,
                success: function(response) {
                    if (response.success) {
                        results.push(`<div class="test-result success"><i class="fas fa-check"></i> Stats API - OK (Total: ${response.data.total})</div>`);
                    } else {
                        results.push(`<div class="test-result warning"><i class="fas fa-exclamation-triangle"></i> Stats API - Response Error: ${response.message}</div>`);
                    }
                    checkCompletion();
                },
                error: function() {
                    results.push(`<div class="test-result error"><i class="fas fa-times"></i> Stats API - Connection Error</div>`);
                    checkCompletion();
                }
            });
            
            // Test Logs API
            $.ajax({
                url: 'api/logs_simple.php?type=all&limit=1',
                method: 'GET',
                dataType: 'json',
                timeout: 5000,
                success: function(response) {
                    if (response.success) {
                        results.push(`<div class="test-result success"><i class="fas fa-check"></i> Logs API - OK (Found: ${response.data.total} logs)</div>`);
                    } else {
                        results.push(`<div class="test-result warning"><i class="fas fa-exclamation-triangle"></i> Logs API - Response Error: ${response.message}</div>`);
                    }
                    checkCompletion();
                },
                error: function() {
                    results.push(`<div class="test-result error"><i class="fas fa-times"></i> Logs API - Connection Error</div>`);
                    checkCompletion();
                }
            });
        }
        
        function testDashboardComponents() {
            $('#componentTestResult').html('<i class="fas fa-spinner fa-spin"></i> Testing dashboard components...');
            
            let results = [];
            
            // Test Bootstrap
            if (typeof bootstrap !== 'undefined') {
                results.push(`<div class="test-result success"><i class="fas fa-check"></i> Bootstrap - OK</div>`);
            } else {
                results.push(`<div class="test-result warning"><i class="fas fa-exclamation-triangle"></i> Bootstrap - Not detected</div>`);
            }
            
            // Test FontAwesome
            const faTest = document.querySelector('.fa, .fas, .far, .fab');
            if (faTest) {
                results.push(`<div class="test-result success"><i class="fas fa-check"></i> FontAwesome - OK</div>`);
            } else {
                results.push(`<div class="test-result warning"><i class="fas fa-exclamation-triangle"></i> FontAwesome - Not detected</div>`);
            }
            
            // Test CSS Files
            const cssFiles = ['dashboard.css'];
            cssFiles.forEach(file => {
                const link = document.querySelector(`link[href*="${file}"]`);
                if (link) {
                    results.push(`<div class="test-result success"><i class="fas fa-check"></i> ${file} - OK</div>`);
                } else {
                    results.push(`<div class="test-result info"><i class="fas fa-info-circle"></i> ${file} - Not found (optional)</div>`);
                }
            });
            
            results.push(`<div class="test-result success"><strong><i class="fas fa-check-circle"></i> Dashboard components are ready!</strong></div>`);
            
            $('#componentTestResult').html(results.join(''));
        }
        
        function testErrorHandling() {
            $('#errorTestResult').html('<i class="fas fa-spinner fa-spin"></i> Testing error handling...');
            
            let results = [];
            
            // Test invalid API call
            $.ajax({
                url: 'api/logs_simple.php?action=invalid',
                method: 'GET',
                dataType: 'json',
                timeout: 3000,
                success: function(response) {
                    if (!response.success) {
                        results.push(`<div class="test-result success"><i class="fas fa-check"></i> Error handling - OK (Invalid action handled properly)</div>`);
                    } else {
                        results.push(`<div class="test-result warning"><i class="fas fa-exclamation-triangle"></i> Error handling - Unexpected success response</div>`);
                    }
                    $('#errorTestResult').html(results.join(''));
                },
                error: function() {
                    results.push(`<div class="test-result success"><i class="fas fa-check"></i> Error handling - OK (Network error handled)</div>`);
                    $('#errorTestResult').html(results.join(''));
                }
            });
        }
        
        function runAllTests() {
            testJavaScriptFunctions();
            setTimeout(() => testAPIEndpoints(), 500);
            setTimeout(() => testDashboardComponents(), 1000);
            setTimeout(() => testErrorHandling(), 1500);
        }
        
        // Auto run basic tests on page load
        $(document).ready(function() {
            setTimeout(testJavaScriptFunctions, 500);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fixed Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .test-container { background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .test-result.success { background: #d4edda; border-left: 4px solid #28a745; color: #155724; }
        .test-result.error { background: #f8d7da; border-left: 4px solid #dc3545; color: #721c24; }
        .test-result.warning { background: #fff3cd; border-left: 4px solid #ffc107; color: #856404; }
        .test-result.info { background: #d1ecf1; border-left: 4px solid #17a2b8; color: #0c5460; }
        .status-icon { font-size: 1.2em; margin-right: 10px; }
        .test-section { margin: 25px 0; }
        .btn-test { margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="text-center mb-4">
                <h1 class="text-primary"><i class="fas fa-check-double"></i> Dashboard Fixed Test</h1>
                <p class="text-muted">Verifikasi final bahwa semua error telah diperbaiki</p>
            </div>

            <!-- Auto Test Results -->
            <div class="test-section">
                <h4><i class="fas fa-robot"></i> Auto Test Results</h4>
                <div id="autoTestResults">
                    <div class="test-result info">
                        <i class="fas fa-spinner fa-spin status-icon"></i>
                        Running automatic tests...
                    </div>
                </div>
            </div>

            <!-- Manual Tests -->
            <div class="test-section">
                <h4><i class="fas fa-hand-pointer"></i> Manual Tests</h4>
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary btn-test w-100" onclick="testLoadStats()">
                            <i class="fas fa-chart-bar"></i> Test Load Stats
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-success btn-test w-100" onclick="testLoadLogs()">
                            <i class="fas fa-list"></i> Test Load Logs
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-info btn-test w-100" onclick="testApplyFilter()">
                            <i class="fas fa-filter"></i> Test Apply Filter
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-warning btn-test w-100" onclick="testResetFilter()">
                            <i class="fas fa-undo"></i> Test Reset Filter
                        </button>
                    </div>
                </div>
                <div id="manualTestResults" class="mt-3"></div>
            </div>

            <!-- Navigation -->
            <div class="test-section">
                <h4><i class="fas fa-compass"></i> Navigation</h4>
                <div class="row">
                    <div class="col-md-4">
                        <a href="dashboard.php" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-tachometer-alt"></i><br>
                            <strong>Dashboard Fixed</strong><br>
                            <small>Menggunakan dashboard_fixed.js</small>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="demo.php" class="btn btn-warning w-100 mb-2">
                            <i class="fas fa-play"></i><br>
                            <strong>Demo</strong><br>
                            <small>UI tanpa database</small>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="test_final.php" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-vial"></i><br>
                            <strong>Test Final</strong><br>
                            <small>Test komprehensif</small>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Status Summary -->
            <div class="test-section">
                <div id="statusSummary" class="text-center">
                    <div class="test-result info">
                        <i class="fas fa-clock status-icon"></i>
                        Waiting for test completion...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/dashboard_fixed.js"></script>
    
    <script>
        let testResults = {
            jsLoaded: false,
            functionsExist: false,
            apiWorking: false,
            noErrors: false
        };

        function runAutoTests() {
            let results = [];
            
            // Test 1: Check if JavaScript file loaded
            if (typeof window.loadStats === 'function') {
                results.push({
                    type: 'success',
                    message: 'dashboard_fixed.js loaded successfully'
                });
                testResults.jsLoaded = true;
            } else {
                results.push({
                    type: 'error',
                    message: 'dashboard_fixed.js failed to load'
                });
            }
            
            // Test 2: Check all required functions
            const requiredFunctions = ['loadStats', 'loadLogs', 'applyFilter', 'resetFilter', 'refreshData', 'changePage', 'toggleSidebar', 'showDetail'];
            let functionsOK = true;
            
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    results.push({
                        type: 'success',
                        message: `Function ${funcName}() exists`
                    });
                } else {
                    results.push({
                        type: 'error',
                        message: `Function ${funcName}() missing`
                    });
                    functionsOK = false;
                }
            });
            
            testResults.functionsExist = functionsOK;
            
            // Test 3: Check for JavaScript errors
            let errorCount = 0;
            window.addEventListener('error', function(e) {
                errorCount++;
            });
            
            setTimeout(() => {
                if (errorCount === 0) {
                    results.push({
                        type: 'success',
                        message: 'No JavaScript errors detected'
                    });
                    testResults.noErrors = true;
                } else {
                    results.push({
                        type: 'error',
                        message: `${errorCount} JavaScript errors detected`
                    });
                }
                
                displayAutoResults(results);
                updateStatusSummary();
            }, 1000);
            
            // Test 4: API Test
            testAPI(results);
        }
        
        function testAPI(results) {
            $.ajax({
                url: 'api/logs_simple.php?action=stats',
                method: 'GET',
                dataType: 'json',
                timeout: 5000,
                success: function(response) {
                    if (response.success) {
                        results.push({
                            type: 'success',
                            message: 'API working correctly'
                        });
                        testResults.apiWorking = true;
                    } else {
                        results.push({
                            type: 'warning',
                            message: 'API responding but with errors'
                        });
                    }
                    displayAutoResults(results);
                    updateStatusSummary();
                },
                error: function() {
                    results.push({
                        type: 'warning',
                        message: 'API connection issues (may be normal if no database)'
                    });
                    displayAutoResults(results);
                    updateStatusSummary();
                }
            });
        }
        
        function displayAutoResults(results) {
            let html = '';
            results.forEach(result => {
                const icon = result.type === 'success' ? 'fa-check' : 
                           result.type === 'error' ? 'fa-times' : 'fa-exclamation-triangle';
                
                html += `
                    <div class="test-result ${result.type}">
                        <i class="fas ${icon} status-icon"></i>
                        ${result.message}
                    </div>
                `;
            });
            
            $('#autoTestResults').html(html);
        }
        
        function updateStatusSummary() {
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(Boolean).length;
            
            let summaryClass = 'success';
            let summaryIcon = 'fa-check-circle';
            let summaryText = '';
            
            if (passedTests === totalTests) {
                summaryText = '🎉 All tests passed! Dashboard is ready to use.';
            } else if (passedTests >= totalTests - 1) {
                summaryClass = 'warning';
                summaryIcon = 'fa-exclamation-triangle';
                summaryText = '⚠️ Most tests passed. Dashboard should work with minor issues.';
            } else {
                summaryClass = 'error';
                summaryIcon = 'fa-times-circle';
                summaryText = '❌ Some tests failed. Please check the issues above.';
            }
            
            $('#statusSummary').html(`
                <div class="test-result ${summaryClass}">
                    <i class="fas ${summaryIcon} status-icon"></i>
                    <strong>${summaryText}</strong><br>
                    <small>Tests passed: ${passedTests}/${totalTests}</small>
                </div>
            `);
        }
        
        // Manual test functions
        function testLoadStats() {
            try {
                window.loadStats();
                addManualResult('success', 'loadStats() executed successfully');
            } catch (e) {
                addManualResult('error', 'loadStats() error: ' + e.message);
            }
        }
        
        function testLoadLogs() {
            try {
                window.loadLogs();
                addManualResult('success', 'loadLogs() executed successfully');
            } catch (e) {
                addManualResult('error', 'loadLogs() error: ' + e.message);
            }
        }
        
        function testApplyFilter() {
            try {
                window.applyFilter();
                addManualResult('success', 'applyFilter() executed successfully');
            } catch (e) {
                addManualResult('error', 'applyFilter() error: ' + e.message);
            }
        }
        
        function testResetFilter() {
            try {
                window.resetFilter();
                addManualResult('success', 'resetFilter() executed successfully');
            } catch (e) {
                addManualResult('error', 'resetFilter() error: ' + e.message);
            }
        }
        
        function addManualResult(type, message) {
            const icon = type === 'success' ? 'fa-check' : 'fa-times';
            const html = `
                <div class="test-result ${type}">
                    <i class="fas ${icon} status-icon"></i>
                    ${message}
                </div>
            `;
            $('#manualTestResults').append(html);
        }
        
        // Run auto tests when page loads
        $(document).ready(function() {
            setTimeout(runAutoTests, 500);
        });
    </script>
</body>
</html>
